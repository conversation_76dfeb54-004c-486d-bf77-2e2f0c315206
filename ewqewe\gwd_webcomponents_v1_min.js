/*

 Copyright (c) 2016 The Polymer Project Authors. All rights reserved.
 This code may only be used under the BSD style license found at
 http://polymer.github.io/LICENSE.txt The complete set of authors may be found
 at http://polymer.github.io/AUTHORS.txt The complete set of contributors may
 be found at http://polymer.github.io/CONTRIBUTORS.txt Code distributed by
 Google as part of the polymer project is also subject to an additional IP
 rights grant found at http://polymer.github.io/PATENTS.txt
*/
(function(){if(window.Reflect!==void 0&&window.customElements!==void 0&&!window.customElements.polyfillWrapFlushCallback){var BuiltInHTMLElement=HTMLElement;window.HTMLElement=function(){return Reflect.construct(BuiltInHTMLElement,[],this.constructor)};HTMLElement.prototype=BuiltInHTMLElement.prototype;HTMLElement.prototype.constructor=HTMLElement;Object.setPrototypeOf(HTMLElement,BuiltInHTMLElement)}})();
(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var m;function n(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright (c) 2016 The Polymer Project Authors. All rights reserved.
 This code may only be used under the BSD style license found at
 http://polymer.github.io/LICENSE.txt The complete set of authors may be found
 at http://polymer.github.io/AUTHORS.txt The complete set of contributors may
 be found at http://polymer.github.io/CONTRIBUTORS.txt Code distributed by
 Google as part of the polymer project is also subject to an additional IP
 rights grant found at http://polymer.github.io/PATENTS.txt
*/
var p=window.Document.prototype.createElement,q=window.Document.prototype.createElementNS,aa=window.Document.prototype.importNode,ba=window.Document.prototype.prepend,ca=window.Document.prototype.append,da=window.DocumentFragment.prototype.prepend,ea=window.DocumentFragment.prototype.append,t=window.Node.prototype.cloneNode,u=window.Node.prototype.appendChild,v=window.Node.prototype.insertBefore,w=window.Node.prototype.removeChild,x=window.Node.prototype.replaceChild,z=Object.getOwnPropertyDescriptor(window.Node.prototype,
"textContent"),A=window.Element.prototype.attachShadow,B=Object.getOwnPropertyDescriptor(window.Element.prototype,"innerHTML"),C=window.Element.prototype.getAttribute,D=window.Element.prototype.setAttribute,E=window.Element.prototype.removeAttribute,F=window.Element.prototype.toggleAttribute,G=window.Element.prototype.getAttributeNS,H=window.Element.prototype.setAttributeNS,fa=window.Element.prototype.removeAttributeNS,ha=window.Element.prototype.insertAdjacentElement,ia=window.Element.prototype.insertAdjacentHTML,
ja=window.Element.prototype.prepend,ka=window.Element.prototype.append,la=window.Element.prototype.before,ma=window.Element.prototype.after,na=window.Element.prototype.replaceWith,oa=window.Element.prototype.remove,pa=window.HTMLElement,I=Object.getOwnPropertyDescriptor(window.HTMLElement.prototype,"innerHTML"),qa=window.HTMLElement.prototype.insertAdjacentElement,ra=window.HTMLElement.prototype.insertAdjacentHTML;var sa=function(){var a=new Set;"annotation-xml color-profile font-face font-face-src font-face-uri font-face-format font-face-name missing-glyph".split(" ").forEach(function(b){return a.add(b)});return a}();function ta(a){var b=sa.has(a);a=/^[a-z][.0-9_a-z]*-[-.0-9_a-z]*$/.test(a);return!b&&a}var ua=document.contains?document.contains.bind(document):document.documentElement.contains.bind(document.documentElement);
function J(a){var b=a.isConnected;if(b!==void 0)return b;if(ua(a))return!0;for(;a&&!(a.__CE_isImportDocument||a instanceof Document);)a=a.parentNode||(window.ShadowRoot&&a instanceof ShadowRoot?a.host:void 0);return!(!a||!(a.__CE_isImportDocument||a instanceof Document))}function K(a){var b=a.children;if(b)return Array.prototype.slice.call(b);b=[];for(a=a.firstChild;a;a=a.nextSibling)a.nodeType===Node.ELEMENT_NODE&&b.push(a);return b}
function L(a,b){for(;b&&b!==a&&!b.nextSibling;)b=b.parentNode;return b&&b!==a?b.nextSibling:null}
function M(a,b,c){for(var e=a;e;){if(e.nodeType===Node.ELEMENT_NODE){var d=e;b(d);var f=d.localName;if(f==="link"&&d.getAttribute("rel")==="import"){e=d.import;c===void 0&&(c=new Set);if(e instanceof Node&&!c.has(e))for(c.add(e),e=e.firstChild;e;e=e.nextSibling)M(e,b,c);e=L(a,d);continue}else if(f==="template"){e=L(a,d);continue}if(d=d.__CE_shadowRoot)for(d=d.firstChild;d;d=d.nextSibling)M(d,b,c)}e=e.firstChild?e.firstChild:L(a,e)}};function va(){var a=!(N==null||!N.noDocumentConstructionObserver),b=!(N==null||!N.shadyDomFastWalk);this.j=[];this.C=[];this.i=!1;this.shadyDomFastWalk=b;this.P=!a}function O(a,b,c,e){var d=window.ShadyDOM;if(a.shadyDomFastWalk&&d&&d.inUse){if(b.nodeType===Node.ELEMENT_NODE&&c(b),b.querySelectorAll)for(a=d.nativeMethods.querySelectorAll.call(b,"*"),b=0;b<a.length;b++)c(a[b])}else M(b,c,e)}function wa(a,b){a.i=!0;a.j.push(b)}function xa(a,b){a.i=!0;a.C.push(b)}
function P(a,b){a.i&&O(a,b,function(c){return Q(a,c)})}function Q(a,b){if(a.i&&!b.__CE_patched){b.__CE_patched=!0;for(var c=0;c<a.j.length;c++)a.j[c](b);for(c=0;c<a.C.length;c++)a.C[c](b)}}function R(a,b){var c=[];O(a,b,function(d){return c.push(d)});for(b=0;b<c.length;b++){var e=c[b];e.__CE_state===1?a.connectedCallback(e):S(a,e)}}function T(a,b){var c=[];O(a,b,function(d){return c.push(d)});for(b=0;b<c.length;b++){var e=c[b];e.__CE_state===1&&a.disconnectedCallback(e)}}
function U(a,b,c){c=c===void 0?{}:c;var e=c.R,d=c.upgrade||function(g){return S(a,g)},f=[];O(a,b,function(g){a.i&&Q(a,g);if(g.localName==="link"&&g.getAttribute("rel")==="import"){var h=g.import;h instanceof Node&&(h.__CE_isImportDocument=!0,h.__CE_registry=document.__CE_registry);h&&h.readyState==="complete"?h.__CE_documentLoadHandled=!0:g.addEventListener("load",function(){var k=g.import;if(!k.__CE_documentLoadHandled){k.__CE_documentLoadHandled=!0;var l=new Set;e&&(e.forEach(function(r){return l.add(r)}),
l.delete(k));U(a,k,{R:l,upgrade:d})}})}else f.push(g)},e);for(b=0;b<f.length;b++)d(f[b])}function S(a,b){try{var c=a.L(b.ownerDocument,b.localName);c&&a.N(b,c)}catch(e){V(e)}}m=va.prototype;
m.N=function(a,b){if(a.__CE_state===void 0){b.constructionStack.push(a);try{try{if(new b.constructorFunction!==a)throw Error("The custom element constructor did not produce the element being upgraded.");}finally{b.constructionStack.pop()}}catch(f){throw a.__CE_state=2,f;}a.__CE_state=1;a.__CE_definition=b;if(b.attributeChangedCallback&&a.hasAttributes()){b=b.observedAttributes;for(var c=0;c<b.length;c++){var e=b[c],d=a.getAttribute(e);d!==null&&this.attributeChangedCallback(a,e,null,d,null)}}J(a)&&
this.connectedCallback(a)}};m.connectedCallback=function(a){var b=a.__CE_definition;if(b.connectedCallback)try{b.connectedCallback.call(a)}catch(c){V(c)}};m.disconnectedCallback=function(a){var b=a.__CE_definition;if(b.disconnectedCallback)try{b.disconnectedCallback.call(a)}catch(c){V(c)}};m.attributeChangedCallback=function(a,b,c,e,d){var f=a.__CE_definition;if(f.attributeChangedCallback&&f.observedAttributes.indexOf(b)>-1)try{f.attributeChangedCallback.call(a,b,c,e,d)}catch(g){V(g)}};
m.L=function(a,b){var c=a.__CE_registry;if(c&&(a.defaultView||a.__CE_isImportDocument))return W(c,b)};
function ya(a,b,c,e){var d=b.__CE_registry;if(d&&(e===null||e==="http://www.w3.org/1999/xhtml")&&(d=W(d,c)))try{var f=new d.constructorFunction;if(f.__CE_state===void 0||f.__CE_definition===void 0)throw Error("Failed to construct '"+c+"': The returned value was not constructed with the HTMLElement constructor.");if(f.namespaceURI!=="http://www.w3.org/1999/xhtml")throw Error("Failed to construct '"+c+"': The constructed element's namespace must be the HTML namespace.");if(f.hasAttributes())throw Error("Failed to construct '"+
c+"': The constructed element must not have any attributes.");if(f.firstChild!==null)throw Error("Failed to construct '"+c+"': The constructed element must not have any children.");if(f.parentNode!==null)throw Error("Failed to construct '"+c+"': The constructed element must not have a parent node.");if(f.ownerDocument!==b)throw Error("Failed to construct '"+c+"': The constructed element's owner document is incorrect.");if(f.localName!==c)throw Error("Failed to construct '"+c+"': The constructed element's local name is incorrect.");
return f}catch(g){return V(g),b=e===null?p.call(b,c):q.call(b,e,c),Object.setPrototypeOf(b,HTMLUnknownElement.prototype),b.__CE_state=2,b.__CE_definition=void 0,Q(a,b),b}b=e===null?p.call(b,c):q.call(b,e,c);Q(a,b);return b}
function V(a){var b="",c="",e=0,d=0;a instanceof Error?(b=a.message,c=a.sourceURL||a.fileName||"",e=a.line||a.lineNumber||0,d=a.column||a.columnNumber||0):b="Uncaught "+String(a);var f=void 0;ErrorEvent.prototype.initErrorEvent===void 0?f=new ErrorEvent("error",{cancelable:!0,message:b,filename:c,lineno:e,colno:d,error:a}):(f=document.createEvent("ErrorEvent"),f.initErrorEvent("error",!1,!0,b,c,e),f.preventDefault=function(){Object.defineProperty(this,"defaultPrevented",{configurable:!0,get:function(){return!0}})});
f.error===void 0&&Object.defineProperty(f,"error",{configurable:!0,enumerable:!0,get:function(){return a}});window.dispatchEvent(f);f.defaultPrevented||console.error(a)};function za(){var a=this;this.J=void 0;this.I=new Promise(function(b){a.M=b})}za.prototype.resolve=function(a){if(this.J)throw Error("Already resolved.");this.J=a;this.M(a)};function X(a){var b=document;this.v=void 0;this.g=a;this.l=b;U(this.g,this.l);this.l.readyState==="loading"&&(this.v=new MutationObserver(this.K.bind(this)),this.v.observe(this.l,{childList:!0,subtree:!0}))}X.prototype.disconnect=function(){this.v&&this.v.disconnect()};X.prototype.K=function(a){var b=this.l.readyState;b!=="interactive"&&b!=="complete"||this.disconnect();for(b=0;b<a.length;b++)for(var c=a[b].addedNodes,e=0;e<c.length;e++)U(this.g,c[e])};function Y(a){this.o=new Map;this.u=new Map;this.F=new Map;this.B=!1;this.D=new Map;this.m=function(b){return b()};this.h=!1;this.A=[];this.g=a;this.G=a.P?new X(a):void 0}m=Y.prototype;m.O=function(a,b){var c=this;if(!(b instanceof Function))throw new TypeError("Custom element constructor getters must be functions.");Aa(this,a);this.o.set(a,b);this.A.push(a);this.h||(this.h=!0,this.m(function(){return c.H()}))};
m.define=function(a,b){var c=this;if(!(b instanceof Function))throw new TypeError("Custom element constructors must be functions.");Aa(this,a);Ba(this,a,b);this.A.push(a);this.h||(this.h=!0,this.m(function(){return c.H()}))};function Aa(a,b){if(!ta(b))throw new SyntaxError("The element name '"+b+"' is not valid.");if(W(a,b)&&!window.enableHotReplacement)throw Error("A custom element with name '"+(b+"' has already been defined."));if(a.B)throw Error("A custom element is already being defined.");}
function Ba(a,b,c){a.B=!0;var e;try{var d=c.prototype;if(!(d instanceof Object))throw new TypeError("The custom element constructor's prototype is not an object.");var f=function(r){var y=d[r];if(y!==void 0&&!(y instanceof Function))throw Error("The '"+r+"' callback must be a function.");return y};var g=f("connectedCallback");var h=f("disconnectedCallback");var k=f("adoptedCallback");var l=(e=f("attributeChangedCallback"))&&c.observedAttributes||[]}catch(r){throw r;}finally{a.B=!1}c={localName:b,
constructorFunction:c,connectedCallback:g,disconnectedCallback:h,adoptedCallback:k,attributeChangedCallback:e,observedAttributes:l,constructionStack:[]};a.u.set(b,c);a.F.set(c.constructorFunction,c);return c}m.upgrade=function(a){U(this.g,a)};
m.H=function(){var a=this;if(this.h!==!1){this.h=!1;for(var b=[],c=this.A,e=new Map,d=0;d<c.length;d++)e.set(c[d],[]);U(this.g,document,{upgrade:function(k){if(k.__CE_state===void 0){var l=k.localName,r=e.get(l);r?r.push(k):a.u.has(l)&&b.push(k)}}});for(d=0;d<b.length;d++)S(this.g,b[d]);for(d=0;d<c.length;d++){for(var f=c[d],g=e.get(f),h=0;h<g.length;h++)S(this.g,g[h]);(f=this.D.get(f))&&f.resolve(void 0)}c.length=0}};m.get=function(a){if(a=W(this,a))return a.constructorFunction};
m.whenDefined=function(a){if(!ta(a))return Promise.reject(new SyntaxError("'"+a+"' is not a valid custom element name."));var b=this.D.get(a);if(b)return b.I;b=new za;this.D.set(a,b);var c=this.u.has(a)||this.o.has(a);a=this.A.indexOf(a)===-1;c&&a&&b.resolve(void 0);return b.I};m.polyfillWrapFlushCallback=function(a){this.G&&this.G.disconnect();var b=this.m;this.m=function(c){return a(function(){return b(c)})}};
function W(a,b){var c=a.u.get(b);if(c)return c;if(c=a.o.get(b)){a.o.delete(b);try{return Ba(a,b,c())}catch(e){V(e)}}}Y.prototype.define=Y.prototype.define;Y.prototype.upgrade=Y.prototype.upgrade;Y.prototype.get=Y.prototype.get;Y.prototype.whenDefined=Y.prototype.whenDefined;Y.prototype.polyfillDefineLazy=Y.prototype.O;Y.prototype.polyfillWrapFlushCallback=Y.prototype.polyfillWrapFlushCallback;function Z(a,b,c){function e(d){return function(){for(var f=n.apply(0,arguments),g=[],h=[],k=0;k<f.length;k++){var l=f[k];l instanceof Element&&J(l)&&h.push(l);if(l instanceof DocumentFragment)for(l=l.firstChild;l;l=l.nextSibling)g.push(l);else g.push(l)}d.apply(this,f);for(f=0;f<h.length;f++)T(a,h[f]);if(J(this))for(h=0;h<g.length;h++)f=g[h],f instanceof Element&&R(a,f)}}c.prepend!==void 0&&(b.prepend=e(c.prepend));c.append!==void 0&&(b.append=e(c.append))};function Ca(a){Document.prototype.createElement=function(b){return ya(a,this,b,null)};Document.prototype.importNode=function(b,c){b=aa.call(this,b,!!c);this.__CE_registry?U(a,b):P(a,b);return b};Document.prototype.createElementNS=function(b,c){return ya(a,this,c,b)};Z(a,Document.prototype,{prepend:ba,append:ca})};function Da(a){function b(e){return function(){for(var d=n.apply(0,arguments),f=[],g=[],h=0;h<d.length;h++){var k=d[h];k instanceof Element&&J(k)&&g.push(k);if(k instanceof DocumentFragment)for(k=k.firstChild;k;k=k.nextSibling)f.push(k);else f.push(k)}e.apply(this,d);for(d=0;d<g.length;d++)T(a,g[d]);if(J(this))for(g=0;g<f.length;g++)d=f[g],d instanceof Element&&R(a,d)}}var c=Element.prototype;la!==void 0&&(c.before=b(la));ma!==void 0&&(c.after=b(ma));na!==void 0&&(c.replaceWith=function(){for(var e=
n.apply(0,arguments),d=[],f=[],g=0;g<e.length;g++){var h=e[g];h instanceof Element&&J(h)&&f.push(h);if(h instanceof DocumentFragment)for(h=h.firstChild;h;h=h.nextSibling)d.push(h);else d.push(h)}g=J(this);na.apply(this,e);for(e=0;e<f.length;e++)T(a,f[e]);if(g)for(T(a,this),f=0;f<d.length;f++)e=d[f],e instanceof Element&&R(a,e)});oa!==void 0&&(c.remove=function(){var e=J(this);oa.call(this);e&&T(a,this)})};function Ea(a){function b(d,f){Object.defineProperty(d,"innerHTML",{enumerable:f.enumerable,configurable:!0,get:f.get,set:function(g){var h=this,k=void 0;J(this)&&(k=[],O(a,this,function(y){y!==h&&k.push(y)}));f.set.call(this,g);if(k)for(var l=0;l<k.length;l++){var r=k[l];r.__CE_state===1&&a.disconnectedCallback(r)}this.ownerDocument.__CE_registry?U(a,this):P(a,this);return g}})}function c(d,f){d.insertAdjacentElement=function(g,h){var k=J(h);g=f.call(this,g,h);k&&T(a,h);J(g)&&R(a,h);return g}}function e(d,
f){function g(h,k){for(var l=[];h!==k;h=h.nextSibling)l.push(h);for(k=0;k<l.length;k++)U(a,l[k])}d.insertAdjacentHTML=function(h,k){h=h.toLowerCase();if(h==="beforebegin"){var l=this.previousSibling;f.call(this,h,k);g(l||this.parentNode.firstChild,this)}else if(h==="afterbegin")l=this.firstChild,f.call(this,h,k),g(this.firstChild,l);else if(h==="beforeend")l=this.lastChild,f.call(this,h,k),g(l||this.firstChild,null);else if(h==="afterend")l=this.nextSibling,f.call(this,h,k),g(this.nextSibling,l);
else throw new SyntaxError("The value provided ("+String(h)+") is not one of 'beforebegin', 'afterbegin', 'beforeend', or 'afterend'.");}}A&&(Element.prototype.attachShadow=function(d){d=A.call(this,d);if(a.i&&!d.__CE_patched){d.__CE_patched=!0;for(var f=0;f<a.j.length;f++)a.j[f](d)}return this.__CE_shadowRoot=d});B&&B.get?b(Element.prototype,B):I&&I.get?b(HTMLElement.prototype,I):xa(a,function(d){b(d,{enumerable:!0,configurable:!0,get:function(){return t.call(this,!0).innerHTML},set:function(f){var g=
this.localName==="template",h=g?this.content:this,k=q.call(document,this.namespaceURI,this.localName);for(k.innerHTML=f;h.childNodes.length>0;)w.call(h,h.childNodes[0]);for(f=g?k.content:k;f.childNodes.length>0;)u.call(h,f.childNodes[0])}})});Element.prototype.setAttribute=function(d,f){if(this.__CE_state!==1)return D.call(this,d,f);var g=C.call(this,d);D.call(this,d,f);f=C.call(this,d);a.attributeChangedCallback(this,d,g,f,null)};Element.prototype.setAttributeNS=function(d,f,g){if(this.__CE_state!==
1)return H.call(this,d,f,g);var h=G.call(this,d,f);H.call(this,d,f,g);g=G.call(this,d,f);a.attributeChangedCallback(this,f,h,g,d)};Element.prototype.removeAttribute=function(d){if(this.__CE_state!==1)return E.call(this,d);var f=C.call(this,d);E.call(this,d);f!==null&&a.attributeChangedCallback(this,d,f,null,null)};F&&(Element.prototype.toggleAttribute=function(d,f){if(this.__CE_state!==1)return F.call(this,d,f);var g=C.call(this,d),h=g!==null;f=F.call(this,d,f);if(h!==f){var k;a==null||(k=a.attributeChangedCallback)==
null||k.call(a,this,d,g,f?"":null,null)}return f});Element.prototype.removeAttributeNS=function(d,f){if(this.__CE_state!==1)return fa.call(this,d,f);var g=G.call(this,d,f);fa.call(this,d,f);var h=G.call(this,d,f);g!==h&&a.attributeChangedCallback(this,f,g,h,d)};qa?c(HTMLElement.prototype,qa):ha&&c(Element.prototype,ha);ra?e(HTMLElement.prototype,ra):ia&&e(Element.prototype,ia);Z(a,Element.prototype,{prepend:ja,append:ka});Da(a)};var Fa={};function Ga(a){function b(){var c=this.constructor;var e=document.__CE_registry.F.get(c);if(!e)throw Error("Failed to construct a custom element: The constructor was not registered with `customElements`.");var d=e.constructionStack;if(d.length===0)return d=p.call(document,e.localName),Object.setPrototypeOf(d,c.prototype),d.__CE_state=1,d.__CE_definition=e,Q(a,d),d;var f=d.length-1,g=d[f];if(g===Fa)throw Error("Failed to construct '"+e.localName+"': This element was already constructed.");d[f]=Fa;
Object.setPrototypeOf(g,c.prototype);Q(a,g);return g}b.prototype=pa.prototype;Object.defineProperty(HTMLElement.prototype,"constructor",{writable:!0,configurable:!0,enumerable:!1,value:b});window.HTMLElement=b};function Ha(a){function b(c,e){Object.defineProperty(c,"textContent",{enumerable:e.enumerable,configurable:!0,get:e.get,set:function(d){if(this.nodeType===Node.TEXT_NODE)e.set.call(this,d);else{var f=void 0;if(this.firstChild){var g=this.childNodes,h=g.length;if(h>0&&J(this)){f=Array(h);for(var k=0;k<h;k++)f[k]=g[k]}}e.set.call(this,d);if(f)for(d=0;d<f.length;d++)T(a,f[d])}}})}Node.prototype.insertBefore=function(c,e){if(c instanceof DocumentFragment){var d=K(c);c=v.call(this,c,e);if(J(this))for(e=
0;e<d.length;e++)R(a,d[e]);return c}d=c instanceof Element&&J(c);e=v.call(this,c,e);d&&T(a,c);J(this)&&R(a,c);return e};Node.prototype.appendChild=function(c){if(c instanceof DocumentFragment){var e=K(c);c=u.call(this,c);if(J(this))for(var d=0;d<e.length;d++)R(a,e[d]);return c}e=c instanceof Element&&J(c);d=u.call(this,c);e&&T(a,c);J(this)&&R(a,c);return d};Node.prototype.cloneNode=function(c){c=t.call(this,!!c);this.ownerDocument.__CE_registry?U(a,c):P(a,c);return c};Node.prototype.removeChild=function(c){var e=
c instanceof Element&&J(c),d=w.call(this,c);e&&T(a,c);return d};Node.prototype.replaceChild=function(c,e){if(c instanceof DocumentFragment){var d=K(c);c=x.call(this,c,e);if(J(this))for(T(a,e),e=0;e<d.length;e++)R(a,d[e]);return c}d=c instanceof Element&&J(c);var f=x.call(this,c,e),g=J(this);g&&T(a,e);d&&T(a,c);g&&R(a,c);return f};z&&z.get?b(Node.prototype,z):wa(a,function(c){b(c,{enumerable:!0,configurable:!0,get:function(){for(var e=[],d=this.firstChild;d;d=d.nextSibling)d.nodeType!==Node.COMMENT_NODE&&
e.push(d.textContent);return e.join("")},set:function(e){for(;this.firstChild;)w.call(this,this.firstChild);e!=null&&e!==""&&u.call(this,document.createTextNode(e))}})})};var N=window.customElements;function Ia(){var a=new va;Ga(a);Ca(a);Z(a,DocumentFragment.prototype,{prepend:da,append:ea});Ha(a);Ea(a);window.CustomElementRegistry=Y;a=new Y(a);document.__CE_registry=a;Object.defineProperty(window,"customElements",{configurable:!0,enumerable:!0,value:a})}N&&!N.forcePolyfill&&typeof N.define=="function"&&typeof N.get=="function"||Ia();window.__CE_installPolyfill=Ia;})();
(function(){var b=window.document;window.WebComponents=window.WebComponents||{};var a=function(){window.removeEventListener("DOMContentLoaded",a);window.WebComponents.ready=!0;var c=b.createEvent("CustomEvent");c.initEvent("WebComponentsReady",!0,!0);setTimeout(function(){window.document.dispatchEvent(c)},0)};b.readyState==="complete"?a():window.addEventListener("DOMContentLoaded",a)})();
