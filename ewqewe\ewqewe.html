<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Google Web Designer 16.4.0.0711">
  <meta name="template" content="Banner 3.0.0">
  <meta name="environment" content="gwd-genericad">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="gwdpage_style.css" rel="stylesheet" data-version="13" data-exports-type="gwd-page">
  <link href="gwdpagedeck_style.css" rel="stylesheet" data-version="14" data-exports-type="gwd-pagedeck">
  <style id="gwd-lightbox-style">
    .gwd-lightbox {
      overflow: hidden;
    }
  </style>
  <style id="gwd-text-style">
    p {
      margin: 0px;
    }
    h1 {
      margin: 0px;
    }
    h2 {
      margin: 0px;
    }
    h3 {
      margin: 0px;
    }
  </style>
  <style>
    html, body {
      width: 100%;
      height: 100%;
      margin: 0px;
    }
    .gwd-page-container {
      position: relative;
      width: 100%;
      height: 100%;
    }
    .gwd-page-content {
      background-color: transparent;
      transform: perspective(1400px) matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
      position: absolute;
      transform-style: preserve-3d;
    }
    .gwd-page-content * {
      transform-style: preserve-3d;
    }
    .gwd-page-wrapper {
      background-color: rgb(255, 255, 255);
      position: absolute;
      transform: translateZ(0px);
    }
    .gwd-page-size {
      width: 728px;
      height: 90px;
    }
  </style>
  <script data-source="gwd_webcomponents_v1_min.js" data-version="2" data-exports-type="gwd_webcomponents_v1" src="gwd_webcomponents_v1_min.js"></script>
  <script data-source="gwdpage_min.js" data-version="13" data-exports-type="gwd-page" src="gwdpage_min.js"></script>
  <script data-source="gwdpagedeck_min.js" data-version="14" data-exports-type="gwd-pagedeck" src="gwdpagedeck_min.js"></script>
  <script data-source="gwdgenericad_min.js" data-version="8" data-exports-type="gwd-genericad" src="gwdgenericad_min.js"></script>
</head>

<body>
  <gwd-genericad id="gwd-ad">
    <gwd-pagedeck class="gwd-page-container" id="pagedeck">
      <gwd-page id="page1" class="gwd-page-wrapper gwd-page-size gwd-lightbox" data-gwd-width="728px" data-gwd-height="90px">
        <div class="gwd-page-content gwd-page-size"></div>
      </gwd-page>
    </gwd-pagedeck>
  </gwd-genericad>
  <script type="text/javascript" id="gwd-init-code">
    (function() {
      var gwdAd = document.getElementById('gwd-ad');

      /**
       * Handles the DOMContentLoaded event. The DOMContentLoaded event is
       * fired when the document has been completely loaded and parsed.
       */
      function handleDomContentLoaded(event) {

      }

      /**
       * Handles the WebComponentsReady event. This event is fired when all
       * custom elements have been registered and upgraded.
       */
      function handleWebComponentsReady(event) {
        // Start the Ad lifecycle.
        requestAnimationFrame(function() {
          setTimeout(function() {
            gwdAd.initAd();
          }, 1);
        });
      }

      /**
       * Handles the event that is dispatched after the Ad has been
       * initialized and before the default page of the Ad is shown.
       */
      function handleAdInitialized(event) {}

      window.addEventListener('DOMContentLoaded',
        handleDomContentLoaded, false);
      window.addEventListener('WebComponentsReady',
        handleWebComponentsReady, false);
      window.addEventListener('adinitialized',
        handleAdInitialized, false);
    })();
  </script>
</body>

</html>