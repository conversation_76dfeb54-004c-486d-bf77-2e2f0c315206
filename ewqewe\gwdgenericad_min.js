(function(){'use strict';var c="function"==typeof Object.create?Object.create:function(a){function b(){}b.prototype=a;return new b},e;if("function"==typeof Object.setPrototypeOf)e=Object.setPrototypeOf;else{var f;a:{var g={a:!0},k={};try{k.__proto__=g;f=k.a;break a}catch(a){}f=!1}e=f?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var l=e;/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var m=this||self;function n(a,b){this.m=a===p&&b||"";this.o=t}n.prototype.i=!0;n.prototype.h=function(){return this.m};var t={},p={};var u=new n(p,"_newtab");function v(a){this.g=a}v.prototype.toString=function(){return this.g.toString()};v.prototype.i=!0;v.prototype.h=function(){return this.g.toString()};var w;try{new URL("s://g"),w=!0}catch(a){w=!1}var x=w,y={};function z(){var a=HTMLElement.call(this)||this;a.i=!1;a.l=a.s.bind(a);a.h=null;a.j=null;return a}var A=HTMLElement;z.prototype=c(A.prototype);z.prototype.constructor=z;if(l)l(z,A);else for(var B in A)if("prototype"!=B)if(Object.defineProperties){var C=Object.getOwnPropertyDescriptor(A,B);C&&Object.defineProperty(z,B,C)}else z[B]=A[B];
z.prototype.connectedCallback=function(){var a=this;this.i||(document.body.style.opacity="0");setTimeout(function(){a.g=a.querySelector("gwd-pagedeck");a.h=document.querySelector("gwd-responsive-attributes-helper");a.h&&a.h.applyOverrides();window.addEventListener("resize",a.l,!1)},1)};z.prototype.disconnectedCallback=function(){window.removeEventListener("resize",this.l,!1)};
z.prototype.initAd=function(){this.i=!0;document.body.style.opacity="";var a=void 0===a?null:a;var b=document.createEvent("CustomEvent");b.initCustomEvent("adinitialized",!0,!0,a);this.dispatchEvent(b);this.goToPage()};z.prototype.goToPage=function(a,b,q,d,h){if(a=void 0!=a?this.g.getPage(a):this.g.getDefaultPage())a=this.g.getOrientationSpecificPage(window.innerHeight>=window.innerWidth?1:2,a.id),void 0!=b?this.g.goToPage(a.id,b,q,d,h):this.g.goToPage(a.id)};
z.prototype.s=function(){this.i&&this.h&&this.h.applyOverrides();var a=window.innerHeight>=window.innerWidth?1:2;this.j!=a&&(this.j=a,(a=this.g.getPage(Number(this.g.currentIndex)))&&this.goToPage(a.id))};
z.prototype.exit=function(a,b,q){b=void 0===b?!1:b;if(!(a instanceof v||a instanceof v)){a="object"==typeof a&&a.i?a.h():String(a);b:{var d=a;if(x){try{var h=new URL(d)}catch(D){d="https:";break b}d=h.protocol}else c:{h=document.createElement("a");try{h.href=d}catch(D){d=void 0;break c}d=h.protocol;d=":"===d||""===d?"https:":d}}"javascript:"===d&&(a="about:invalid#zClosurez");a=new v(a,y)}var r;u instanceof n?r=u instanceof n&&u.constructor===n&&u.o===t?u.m:"type_error:Const":r=u||"";m.open(a instanceof
v&&a.constructor===v?a.g:"type_error:SafeUrl",r);b&&this.goToPage(q)};customElements.define("gwd-genericad",z);}).call(this);
