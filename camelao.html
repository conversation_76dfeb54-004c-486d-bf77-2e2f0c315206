<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Shopping Camelão - Abra sua loja</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@700;900&display=swap');
        html { scroll-behavior: smooth; }
        body {
            margin: 0;
            font-family: 'Montserrat', Arial, sans-serif;
            background: #fff;
            color: #193b6a;
            box-sizing: border-box;
        }
        .topbar {
            height: 12px;
            background: #F8B400;
        }
        header {
            background: #193b6a;
            color: #fff;
            position: relative;
            min-height: 220px;
        }
        .header-bg {
            width: 100%;
            max-height: 340px;
            object-fit: cover;
            display: block;
        }
        .header-content {
            position: absolute;
            top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.34);
            display: flex; flex-direction: column; align-items: center; justify-content: center;
            padding: 18px 10px 0 10px;
        }
        .header-logo {
            margin-bottom: 14px;
            width: 170px;
            max-width: 80vw;
        }
        .header-title {
            font-size: 2.1rem;
            font-weight: 900;
            text-align: center;
            text-shadow: 1px 2px 9px #000a;
            margin-bottom: 10px;
            line-height: 1.13;
        }
        .header-subtitle {
            font-size: 1.15rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 19px;
            text-shadow: 1px 1px 6px #000a;
            line-height: 1.18;
        }
        .btn-green {
            background: #27ae60;
            color: #fff;
            font-weight: bold;
            border: none;
            border-radius: 22px;
            padding: 15px 36px;
            font-size: 1.14rem;
            cursor: pointer;
            box-shadow: 0 4px 16px #0002;
            transition: background .2s, transform .12s;
            outline: none;
        }
        .btn-green:hover, .btn-green:focus {
            background: #219150;
            transform: scale(1.03);
        }
        .numbers {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 28px;
            background: #fff;
            padding: 34px 8px 20px 8px;
            max-width: 1050px;
            margin: 0 auto;
        }
        .number-box {
            background: #ffcd39;
            color: #1a2d4d;
            border-radius: 10px;
            padding: 20px 28px 10px 28px;
            min-width: 110px;
            font-weight: bold;
            font-size: 1.44rem;
            text-align: center;
            box-shadow: 0 3px 10px #0001;
        }
        .number-box:last-child {
            background: #e0e0e0;
            color: #193b6a;
            min-width: 120px;
        }
        .number-small {
            font-size: .96rem;
            font-weight: 600;
            display: block;
            margin-top: 6px;
            color: #47556b;
        }
        .section-title {
            text-align: center;
            font-size: 1.33rem;
            font-weight: 900;
            margin: 42px 0 18px 0;
            color: #193b6a;
        }
        .center-img {
            display: block;
            margin: 0 auto 12px auto;
            max-width: 96%;
            border-radius: 8px;
            box-shadow: 0 2px 12px #0001;
        }
        .depoimentos-section {
            background: #f5f7fa;
            padding: 38px 0 28px 0;
        }
        .depoimentos-title {
            text-align: center;
            font-size: 1.26rem;
            font-weight: 800;
            color: #193b6a;
            margin-bottom: 34px;
        }
        .depoimentos-list {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            margin-bottom: 22px;
            max-width: 1050px;
            margin-left: auto;
            margin-right: auto;
        }
        .depoimento {
            background: #fff;
            border-radius: 10px;
            padding: 16px 18px 12px 18px;
            text-align: center;
            width: 210px;
            box-shadow: 0 3px 18px #0001;
        }
        .depoimento-img {
            width: 74px; height: 74px; border-radius: 50%; margin-bottom: 11px; object-fit: cover; background: #eee;
        }
        .depoimento-nome {
            font-weight: bold;
            font-size: 1.05rem;
            margin-bottom: 6px;
            color: #193b6a;
        }
        .depoimento-desc {
            font-size: .97rem;
            color: #29364d;
        }
        .lojas-sucesso-list {
            display: flex;
            justify-content: center;
            gap: 32px;
            margin-bottom: 28px;
            flex-wrap: wrap;
            max-width: 1080px;
            margin-left: auto;
            margin-right: auto;
        }
        .loja-sucesso {
            background: #fff;
            border-radius: 8px;
            padding: 13px 12px 8px 12px;
            width: 153px;
            box-shadow: 0 2px 12px #0001;
            text-align: center;
        }
        .loja-sucesso-logo {
            height: 45px; margin-bottom: 9px; object-fit: contain;
        }
        .loja-sucesso-desc {
            font-size: .95rem;
            color: #29364d;
        }
        .cta-section {
            text-align: center;
            padding: 37px 0 22px 0;
        }
        .cta-title {
            color: #F8B400;
            font-size: 2.05rem;
            font-weight: 900;
            letter-spacing: 1px;
            text-shadow: 1px 2px 8px #fff5;
        }
        .cta-subtitle {
            color: #193b6a;
            font-size: 1.28rem;
            font-weight: 900;
            letter-spacing: .4px;
            margin-bottom: 20px;
        }
        .form-section {
            background: #f7f6f4;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: flex-start;
            gap: 34px;
            padding: 38px 0 26px 0;
            max-width: 1100px;
            margin: 0 auto;
        }
        .form-info {
            font-size: 1.12rem;
            font-weight: bold;
            color: #193b6a;
            margin-bottom: 18px;
            max-width: 400px;
        }
        .form-list {
            font-size: 1.07rem;
            margin-bottom: 16px;
            color: #295c7b;
            line-height: 1.7;
            padding-left: 17px;
        }
        .form-box {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px #0002;
            padding: 26px 20px;
            width: 315px;
            max-width: 96vw;
            display: flex;
            flex-direction: column;
            gap: 13px;
        }
        .form-box input,
        .form-box textarea,
        .form-box select {
            width: 100%;
            padding: 10px 11px;
            border-radius: 5px;
            border: 1px solid #d0d0d0;
            font-size: 1rem;
            background: #fafafa;
            margin-bottom: 4px;
        }
        .form-box label {
            font-size: 1rem;
            font-weight: bold;
            margin-bottom: 2px;
        }
        .form-box textarea {
            resize: vertical;
            min-height: 42px;
            max-height: 115px;
        }
        .form-box .checkbox-label {
            font-size: .93rem;
            font-weight: normal;
            display: flex;
            align-items: center;
            margin-top: 2px;
        }
        .form-box .checkbox-label input[type="checkbox"] {
            width: 16px; height: 16px; margin-right: 7px;
        }
        .form-box .btn-green {
            width: 100%;
            padding: 12px 0;
            font-size: 1.04rem;
            border-radius: 8px;
            margin-top: 7px;
        }
        .footer {
            background: #ede8df;
            padding: 29px 0 8px 0;
            text-align: center;
        }
        .footer-logo {
            width: 140px;
            margin-bottom: 9px;
        }
        .footer-text {
            font-size: 1.13rem;
            color: #193b6a;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .footer-copy {
            color: #29364d;
            font-size: .98rem;
        }

        /* --- MEDIA QUERIES --- */
        @media (max-width: 1050px) {
            .numbers, .depoimentos-list, .lojas-sucesso-list, .form-section {max-width: 98vw;}
        }
        @media (max-width: 850px) {
            .numbers, .depoimentos-list, .lojas-sucesso-list, .form-section {
                flex-direction: column; align-items: center; gap: 23px;
            }
            .numbers { gap: 10px; }
            .cta-title { font-size: 1.5rem; }
            .cta-subtitle { font-size: 1rem; }
        }
        @media (max-width: 650px) {
            .header-title { font-size: 1.13rem; }
            .header-subtitle { font-size: .99rem; }
            .numbers { flex-direction: column; }
            .number-box { min-width: 100px; font-size: 1.09rem; padding: 13px 7px 6px 7px;}
            .section-title, .depoimentos-title { font-size: .98rem;}
            .depoimentos-list, .lojas-sucesso-list { flex-direction: column; gap: 11px;}
            .form-box { width: 99vw; max-width: 99vw;}
            .form-section { flex-direction: column; padding: 18px 0 12px 0; }
            .center-img { max-width: 99vw;}
        }
        @media (max-width: 430px) {
            .header-title { font-size: .92rem; }
            .header-logo { width: 90vw; }
            .btn-green { font-size: .99rem; padding: 10px 12vw;}
            .section-title { font-size: .98rem;}
            .form-info { font-size: .96rem;}
        }
    </style>
</head>
<body>
    <div class="topbar" aria-hidden="true"></div>
    <header>
        <img src="FACHADA_CAMELAO.jpg" class="header-bg" alt="Fachada Shopping Camelão" />
        <div class="header-content">
            <img src="LOGO_CAMELAO.png" class="header-logo" alt="Logo Camelão" />
            <h1 class="header-title">ABRA SUA LOJA</h1>
            <div class="header-subtitle">SEU PRÓXIMO GRANDE NEGÓCIO<br>COMEÇA AQUI</div>
            <a href="#formulario" class="btn-green" style="text-decoration:none;">ALUGUE SUA LOJA</a>
        </div>
    </header>

    <!-- Estatísticas -->
    <section class="numbers" aria-label="Estatísticas do shopping">
        <div class="number-box">
            +190<br>
            <span class="number-small">LOJAS ATIVAS</span>
        </div>
        <div class="number-box">
            30<br>
            <span class="number-small">ANOS DE SUCESSO</span>
        </div>
        <div class="number-box">
            +1MILHÃO<br>
            <span class="number-small">POPULAÇÃO ATENDIDA</span>
        </div>
        <div class="number-box" title="Estacionamento privativo">
            <span style="font-size:2.2rem;">&#128663;</span>
            <span class="number-small">ESTACIONAMENTO<br>PRIVATIVO</span>
        </div>
    </section>
    <div class="section-title">GRANDE FLORIANÓPOLIS<br>SÃO JOSÉ E PALHOÇA</div>
    <img src="UNIDADE_CAMELAO.jpg" alt="Unidade Shopping Camelão" class="center-img" style="max-width:360px;">
    <div style="text-align:center;margin:16px 0;">
        <a href="#formulario" class="btn-green" style="text-decoration:none;">ALUGUE SUA LOJA</a>
    </div>
    <!-- Depoimentos -->
    <section class="depoimentos-section">
        <div class="depoimentos-title">DEPOIMENTOS DOS LOJISTAS</div>
        <div class="depoimentos-list">
            <div class="depoimento">
                <img src="DEPOIMENTO1.jpg" alt="Depoimento Lojista 1" class="depoimento-img">
                <div class="depoimento-nome">Nome do Lojista</div>
                <div class="depoimento-desc">Depoimento breve aqui (substitua pelo real)</div>
            </div>
            <div class="depoimento">
                <img src="DEPOIMENTO2.jpg" alt="Depoimento Lojista 2" class="depoimento-img">
                <div class="depoimento-nome">Nome do Lojista</div>
                <div class="depoimento-desc">Depoimento breve aqui (substitua pelo real)</div>
            </div>
            <div class="depoimento">
                <img src="DEPOIMENTO3.jpg" alt="Depoimento Lojista 3" class="depoimento-img">
                <div class="depoimento-nome">Nome do Lojista</div>
                <div class="depoimento-desc">Depoimento breve aqui (substitua pelo real)</div>
            </div>
        </div>
        <!-- Lojistas de sucesso -->
        <div class="section-title" style="margin-top:20px;">LOJISTAS DE SUCESSO</div>
        <div class="lojas-sucesso-list">
            <div class="loja-sucesso">
                <img src="LOGO_HERING.png" class="loja-sucesso-logo" alt="Logo Hering">
                <div class="loja-sucesso-desc">Uma das maiores redes de moda do Brasil, presente em nossas unidades.</div>
            </div>
            <div class="loja-sucesso">
                <img src="LOGO_CACAU_SHOW.png" class="loja-sucesso-logo" alt="Logo Cacau Show">
                <div class="loja-sucesso-desc">A maior rede de chocolates finos do mundo, com lojas no Camelão.</div>
            </div>
            <div class="loja-sucesso">
                <img src="LOGO_NASHIRIVA.png" class="loja-sucesso-logo" alt="Logo Nashiriva">
                <div class="loja-sucesso-desc">Uma marca local de sucesso no segmento de alimentação.</div>
            </div>
            <div class="loja-sucesso">
                <img src="LOGO_SIMON.png" class="loja-sucesso-logo" alt="Logo Simon">
                <div class="loja-sucesso-desc">Referência em seu segmento, exemplo de crescimento no Camelão.</div>
            </div>
        </div>
        <div style="text-align:center;margin:26px 0 0 0;">
            <a href="#formulario" class="btn-green" style="text-decoration:none;">ALUGUE SUA LOJA</a>
        </div>
    </section>
    <!-- Vantagens + Formulário -->
    <section class="form-section" id="formulario">
        <div>
            <div class="form-info">
                Com foco estratégico na unidade de Palhoça, o Shopping Camelão se posiciona como o maior e mais completo centro de compras populares da cidade, oferecendo uma oportunidade ímpar para lojistas e franqueados.
            </div>
            <ul class="form-list">
                <li>Centro de Serviços Completo</li>
                <li>Mais de 260 Vagas de Estacionamento</li>
                <li>Estrutura Moderna e Acessível</li>
                <li>Eventos e Ações Sociais Frequentes</li>
            </ul>
            <a href="#formulario" class="btn-green" style="text-decoration:none;">ALUGUE SUA LOJA</a>
        </div>
        <form class="form-box" autocomplete="off" method="post">
            <label for="nome">Nome Completo</label>
            <input type="text" id="nome" name="nome" required autocomplete="name">
            <label for="email">E-mail</label>
            <input type="email" id="email" name="email" required autocomplete="email">
            <label for="telefone">Telefone/WhatsApp</label>
            <input type="text" id="telefone" name="telefone" required autocomplete="tel">
            <label for="tipo_negocio">Tipo de Negócio (ou Segmento de Interesse)</label>
            <input type="text" id="tipo_negocio" name="tipo_negocio">
            <label for="unidade">Unidade de Interesse</label>
            <select id="unidade" name="unidade">
                <option>São José</option>
                <option>Palhoça</option>
            </select>
            <label for="mensagem">Mensagem</label>
            <textarea id="mensagem" name="mensagem"></textarea>
            <label class="checkbox-label">
                <input type="checkbox" required>
                Declaro que li e concordo com a Política de Privacidade e com o tratamento dos meus dados para contato comercial (LGPD).
            </label>
            <button type="submit" class="btn-green">Enviar Solicitação</button>
        </form>
    </section>
    <!-- Chamada final -->
    <section class="cta-section">
        <div class="cta-title">SEJA UM LOJISTA</div>
        <div class="cta-subtitle">NO SHOPPING CAMELÃO</div>
        <a href="#formulario" class="btn-green" style="text-decoration:none;">ALUGUE SUA LOJA</a>
    </section>
    <!-- Rodapé -->
    <footer class="footer">
        <img src="LOGO_CAMELAO.png" class="footer-logo" alt="Logo Camelão">
        <div class="footer-text">SEU PRÓXIMO GRANDE NEGÓCIO<br>COMEÇA AQUI</div>
        <div class="footer-copy">© 2025 Shopping Camelão. Todos os direitos reservados.</div>
    </footer>
</body>
</html>
