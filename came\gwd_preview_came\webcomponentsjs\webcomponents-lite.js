(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var n;function aa(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}function ba(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var ca=ba(this);
function t(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");}function x(a){if(!(a instanceof Array)){a=t(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a}function y(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright (c) 2016 The Polymer Project Authors. All rights reserved.
 This code may only be used under the BSD style license found at
 http://polymer.github.io/LICENSE.txt The complete set of authors may be found
 at http://polymer.github.io/AUTHORS.txt The complete set of contributors may
 be found at http://polymer.github.io/CONTRIBUTORS.txt Code distributed by
 Google as part of the polymer project is also subject to an additional IP
 rights grant found at http://polymer.github.io/PATENTS.txt
*/
Array.from||(Array.from=function(a){return[].slice.call(a)});Object.assign||(Object.assign=function(a){for(var b=[].slice.call(arguments,1),c=0,d;c<b.length;c++)if(d=b[c])for(var e=a,f=Object.keys(d),g=0;g<f.length;g++){var h=f[g];e[h]=d[h]}return a});/*


 Copyright (c) 2014 Taylor Hakes
 Copyright (c) 2014 Forbes Lindesay

 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 THE SOFTWARE.
*/
var da=setTimeout;function ea(){}function fa(a,b){return function(){a.apply(b,arguments)}}function C(a){if(!(this instanceof C))throw new TypeError("Promises must be constructed via new");if(typeof a!=="function")throw new TypeError("not a function");this.D=0;this.Xa=!1;this.u=void 0;this.V=[];ha(a,this)}
function ia(a,b){for(;a.D===3;)a=a.u;a.D===0?a.V.push(b):(a.Xa=!0,ja(function(){var c=a.D===1?b.Pc:b.Qc;if(c===null)(a.D===1?ka:la)(b.promise,a.u);else{try{var d=c(a.u)}catch(e){la(b.promise,e);return}ka(b.promise,d)}}))}
function ka(a,b){try{if(b===a)throw new TypeError("A promise cannot be resolved with itself.");if(b&&(typeof b==="object"||typeof b==="function")){var c=b.then;if(b instanceof C){a.D=3;a.u=b;ma(a);return}if(typeof c==="function"){ha(fa(c,b),a);return}}a.D=1;a.u=b;ma(a)}catch(d){la(a,d)}}function la(a,b){a.D=2;a.u=b;ma(a)}
function ma(a){a.D===2&&a.V.length===0&&ja(function(){a.Xa||typeof console!=="undefined"&&console&&console.warn("Possible Unhandled Promise Rejection:",a.u)});for(var b=0,c=a.V.length;b<c;b++)ia(a,a.V[b]);a.V=null}function na(a,b,c){this.Pc=typeof a==="function"?a:null;this.Qc=typeof b==="function"?b:null;this.promise=c}function ha(a,b){var c=!1;try{a(function(d){c||(c=!0,ka(b,d))},function(d){c||(c=!0,la(b,d))})}catch(d){c||(c=!0,la(b,d))}}C.prototype["catch"]=function(a){return this.then(null,a)};
C.prototype.then=function(a,b){var c=new this.constructor(ea);ia(this,new na(a,b,c));return c};C.prototype["finally"]=function(a){var b=this.constructor;return this.then(function(c){return b.resolve(a()).then(function(){return c})},function(c){return b.resolve(a()).then(function(){return b.reject(c)})})};
function oa(a){return new C(function(b,c){function d(h,k){try{if(k&&(typeof k==="object"||typeof k==="function")){var l=k.then;if(typeof l==="function"){l.call(k,function(p){d(h,p)},c);return}}e[h]=k;--f===0&&b(e)}catch(p){c(p)}}if(!a||typeof a.length==="undefined")throw new TypeError("Promise.all accepts an array");var e=Array.prototype.slice.call(a);if(e.length===0)return b([]);for(var f=e.length,g=0;g<e.length;g++)d(g,e[g])})}
function pa(a){return a&&typeof a==="object"&&a.constructor===C?a:new C(function(b){b(a)})}function qa(a){return new C(function(b,c){c(a)})}function ra(a){return new C(function(b,c){for(var d=0,e=a.length;d<e;d++)a[d].then(b,c)})}var ja=typeof setImmediate==="function"&&function(a){setImmediate(a)}||function(a){da(a,0)};/*

 Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
 This code may only be used under the BSD style license found at
 http://polymer.github.io/LICENSE.txt The complete set of authors may be found at
 http://polymer.github.io/AUTHORS.txt The complete set of contributors may be
 found at http://polymer.github.io/CONTRIBUTORS.txt Code distributed by Google as
 part of the polymer project is also subject to an additional IP rights grant
 found at http://polymer.github.io/PATENTS.txt
*/
if(!window.Promise){window.Promise=C;C.prototype.then=C.prototype.then;C.all=oa;C.race=ra;C.resolve=pa;C.reject=qa;var sa=document.createTextNode(""),ta=[];(new MutationObserver(function(){for(var a=ta.length,b=0;b<a;b++)ta[b]();ta.splice(0,a)})).observe(sa,{characterData:!0});ja=function(a){ta.push(a);sa.textContent=sa.textContent.length>0?"":"a"}};var ua=document.createEvent("Event");ua.initEvent("foo",!0,!0);ua.preventDefault();if(!ua.defaultPrevented){var va=Event.prototype.preventDefault;Event.prototype.preventDefault=function(){this.cancelable&&(va.call(this),Object.defineProperty(this,"defaultPrevented",{get:function(){return!0},configurable:!0}))}}var wa=/Trident/.test(navigator.userAgent);
if(!window.Event||wa&&typeof window.Event!=="function"){var xa=window.Event;window.Event=function(a,b){b=b||{};var c=document.createEvent("Event");c.initEvent(a,!!b.bubbles,!!b.cancelable);return c};if(xa){for(var ya in xa)window.Event[ya]=xa[ya];window.Event.prototype=xa.prototype}}
if(!window.CustomEvent||wa&&typeof window.CustomEvent!=="function")window.CustomEvent=function(a,b){b=b||{};var c=document.createEvent("CustomEvent");c.initCustomEvent(a,!!b.bubbles,!!b.cancelable,b.detail);return c},window.CustomEvent.prototype=window.Event.prototype;
if(!window.MouseEvent||wa&&typeof window.MouseEvent!=="function"){var za=window.MouseEvent;window.MouseEvent=function(a,b){b=b||{};var c=document.createEvent("MouseEvent");c.initMouseEvent(a,!!b.bubbles,!!b.cancelable,b.view||window,b.detail,b.screenX,b.screenY,b.clientX,b.clientY,b.ctrlKey,b.altKey,b.shiftKey,b.metaKey,b.button,b.relatedTarget);return c};if(za)for(var Aa in za)window.MouseEvent[Aa]=za[Aa];window.MouseEvent.prototype=za.prototype};var Ba=function(){function a(){e++}var b=!1,c=!1,d={get capture(){return b=!0},get once(){return c=!0}},e=0,f=document.createElement("div");f.addEventListener("click",a,d);var g=b&&c;g&&(f.dispatchEvent(new Event("click")),f.dispatchEvent(new Event("click")),g=e==1);f.removeEventListener("click",a,d);return g}(),Da,Ea=(Da=window.EventTarget)!=null?Da:window.Node;
if(!Ba&&"addEventListener"in Ea.prototype){var Fa=function(a){if(!a||typeof a!=="object"&&typeof a!=="function"){var b=!!a;a=!1}else b=!!a.capture,a=!!a.once;return{capture:b,once:a}},Ga=Ea.prototype.addEventListener,Ha=Ea.prototype.removeEventListener,Ia=new WeakMap,Ja=new WeakMap,Ka=function(a,b,c){var d=c?Ia:Ja;c=d.get(a);c===void 0&&d.set(a,c=new Map);a=c.get(b);a===void 0&&c.set(b,a=new WeakMap);return a};Ea.prototype.addEventListener=function(a,b,c){var d=this;if(b!=null){c=Fa(c);var e=c.capture;
c=c.once;var f=Ka(this,a,e);if(!f.has(b)){var g=c?function(k){f.delete(b);Ha.call(d,a,g,e);if(typeof b==="function")return b.call(d,k);if(typeof(b==null?void 0:b.handleEvent)==="function")return b.handleEvent(k)}:null;f.set(b,g);var h;Ga.call(this,a,(h=g)!=null?h:b,e)}}};Ea.prototype.removeEventListener=function(a,b,c){if(b!=null){c=Fa(c).capture;var d=Ka(this,a,c),e=d.get(b);e!==void 0&&(d.delete(b),Ha.call(this,a,e!=null?e:b,c))}}};/*

 Copyright (c) 2020 The Polymer Project Authors. All rights reserved.
 This code may only be used under the BSD style license found at
 http://polymer.github.io/LICENSE.txt The complete set of authors may be found at
 http://polymer.github.io/AUTHORS.txt The complete set of contributors may be
 found at http://polymer.github.io/CONTRIBUTORS.txt Code distributed by Google as
 part of the polymer project is also subject to an additional IP rights grant
 found at http://polymer.github.io/PATENTS.txt
*/
var La=Element.prototype,Ma,Na,Oa,Pa=(Oa=(Na=(Ma=Object.getOwnPropertyDescriptor(La,"attributes"))!=null?Ma:Object.getOwnPropertyDescriptor(Node.prototype,"attributes"))==null?void 0:Na.get)!=null?Oa:function(){return this.attributes},Qa=Array.prototype.map;La.hasOwnProperty("getAttributeNames")||(La.getAttributeNames=function(){return Qa.call(Pa.call(this),function(a){return a.name})});var Ra=Element.prototype;if(!Ra.hasOwnProperty("matches")){var Ua;Ra.matches=(Ua=Ra.webkitMatchesSelector)!=null?Ua:Ra.msMatchesSelector};/*

 Copyright (c) 2020 The Polymer Project Authors. All rights reserved.
 This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
 The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
 The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
 Code distributed by Google as part of the polymer project is also
 subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
*/
var Va=Node.prototype.appendChild;function Wa(a){a=a.prototype;a.hasOwnProperty("append")||Object.defineProperty(a,"append",{configurable:!0,enumerable:!0,writable:!0,value:function(){for(var b=t(y.apply(0,arguments)),c=b.next();!c.done;c=b.next())c=c.value,Va.call(this,typeof c==="string"?document.createTextNode(c):c)}})}Wa(Document);Wa(DocumentFragment);Wa(Element);var Xa=Node.prototype.insertBefore,Ya,Za,$a=(Za=(Ya=Object.getOwnPropertyDescriptor(Node.prototype,"firstChild"))==null?void 0:Ya.get)!=null?Za:function(){return this.firstChild};function ab(a){a=a.prototype;a.hasOwnProperty("prepend")||Object.defineProperty(a,"prepend",{configurable:!0,enumerable:!0,writable:!0,value:function(){var b=y.apply(0,arguments),c=$a.call(this);b=t(b);for(var d=b.next();!d.done;d=b.next())d=d.value,Xa.call(this,typeof d==="string"?document.createTextNode(d):d,c)}})}ab(Document);
ab(DocumentFragment);ab(Element);var bb=Node.prototype.appendChild,cb=Node.prototype.removeChild,db,eb,fb=(eb=(db=Object.getOwnPropertyDescriptor(Node.prototype,"firstChild"))==null?void 0:db.get)!=null?eb:function(){return this.firstChild};
function gb(a){a=a.prototype;a.hasOwnProperty("replaceChildren")||Object.defineProperty(a,"replaceChildren",{configurable:!0,enumerable:!0,writable:!0,value:function(){for(var b=y.apply(0,arguments),c;(c=fb.call(this))!==null;)cb.call(this,c);b=t(b);for(c=b.next();!c.done;c=b.next())c=c.value,bb.call(this,typeof c==="string"?document.createTextNode(c):c)}})}gb(Document);gb(DocumentFragment);gb(Element);var hb=Node.prototype.insertBefore,ib,jb,kb=(jb=(ib=Object.getOwnPropertyDescriptor(Node.prototype,"parentNode"))==null?void 0:ib.get)!=null?jb:function(){return this.parentNode},lb,mb,nb=(mb=(lb=Object.getOwnPropertyDescriptor(Node.prototype,"nextSibling"))==null?void 0:lb.get)!=null?mb:function(){return this.nextSibling};
function ob(a){a=a.prototype;a.hasOwnProperty("after")||Object.defineProperty(a,"after",{configurable:!0,enumerable:!0,writable:!0,value:function(){var b=y.apply(0,arguments),c=kb.call(this);if(c!==null){var d=nb.call(this);b=t(b);for(var e=b.next();!e.done;e=b.next())e=e.value,hb.call(c,typeof e==="string"?document.createTextNode(e):e,d)}}})}ob(CharacterData);ob(Element);var pb=Node.prototype.insertBefore,qb,rb,sb=(rb=(qb=Object.getOwnPropertyDescriptor(Node.prototype,"parentNode"))==null?void 0:qb.get)!=null?rb:function(){return this.parentNode};
function tb(a){a=a.prototype;a.hasOwnProperty("before")||Object.defineProperty(a,"before",{configurable:!0,enumerable:!0,writable:!0,value:function(){var b=y.apply(0,arguments),c=sb.call(this);if(c!==null){b=t(b);for(var d=b.next();!d.done;d=b.next())d=d.value,pb.call(c,typeof d==="string"?document.createTextNode(d):d,this)}}})}tb(CharacterData);tb(Element);var ub=Node.prototype.removeChild,vb,wb,xb=(wb=(vb=Object.getOwnPropertyDescriptor(Node.prototype,"parentNode"))==null?void 0:vb.get)!=null?wb:function(){return this.parentNode};function yb(a){a=a.prototype;a.hasOwnProperty("remove")||Object.defineProperty(a,"remove",{configurable:!0,enumerable:!0,writable:!0,value:function(){var b=xb.call(this);b&&ub.call(b,this)}})}yb(CharacterData);yb(Element);var zb=Node.prototype.insertBefore,Ab=Node.prototype.removeChild,Bb,Cb,Db=(Cb=(Bb=Object.getOwnPropertyDescriptor(Node.prototype,"parentNode"))==null?void 0:Bb.get)!=null?Cb:function(){return this.parentNode};
function Eb(a){a=a.prototype;a.hasOwnProperty("replaceWith")||Object.defineProperty(a,"replaceWith",{configurable:!0,enumerable:!0,writable:!0,value:function(){var b=y.apply(0,arguments),c=Db.call(this);if(c!==null){b=t(b);for(var d=b.next();!d.done;d=b.next())d=d.value,zb.call(c,typeof d==="string"?document.createTextNode(d):d,this);Ab.call(c,this)}}})}Eb(CharacterData);Eb(Element);var Fb=window.Element.prototype,Gb=window.HTMLElement.prototype,Hb=window.SVGElement.prototype;!Gb.hasOwnProperty("classList")||Fb.hasOwnProperty("classList")||Hb.hasOwnProperty("classList")||Object.defineProperty(Fb,"classList",Object.getOwnPropertyDescriptor(Gb,"classList"));/*

 Copyright (c) 2014 The Polymer Project Authors. All rights reserved.
 This code may only be used under the BSD style license found at
 http://polymer.github.io/LICENSE.txt The complete set of authors may be found
 at http://polymer.github.io/AUTHORS.txt The complete set of contributors may
 be found at http://polymer.github.io/CONTRIBUTORS.txt Code distributed by
 Google as part of the polymer project is also subject to an additional IP
 rights grant found at http://polymer.github.io/PATENTS.txt
*/
var Lb=document.createElement("style");Lb.textContent="body {transition: opacity ease-in 0.2s; } \nbody[unresolved] {opacity: 0; display: block; overflow: hidden; position: relative; } \n";var Mb=document.querySelector("head");Mb.insertBefore(Lb,Mb.firstChild);var Nb=window;Nb.WebComponents=Nb.WebComponents||{flags:{}};var Ob=document.querySelector('script[src*="webcomponents-lite.js"]'),Pb=/wc-(.+)/,D={};if(!D.noOpts){location.search.slice(1).split("&").forEach(function(a){a=a.split("=");var b;a[0]&&(b=a[0].match(Pb))&&(D[b[1]]=a[1]||!0)});if(Ob)for(var Qb=0,Rb=void 0;Rb=Ob.attributes[Qb];Qb++)Rb.name!=="src"&&(D[Rb.name]=Rb.value||!0);var Sb={};D.log&&D.log.split&&D.log.split(",").forEach(function(a){Sb[a]=!0});D.log=Sb}Nb.WebComponents.flags=D;
var Tb=D.shadydom;Tb&&(Nb.ShadyDOM=Nb.ShadyDOM||{},Nb.ShadyDOM.force=Tb);var Ub=D.register||D.ce;Ub&&window.customElements&&(Nb.customElements.forcePolyfill=Ub);/*

 Copyright (c) 2016 The Polymer Project Authors. All rights reserved.
 This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
 The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
 The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
 Code distributed by Google as part of the polymer project is also
 subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
*/
(function(a){function b(m,q){if(typeof window.CustomEvent==="function")return new CustomEvent(m,q);var r=document.createEvent("CustomEvent");r.initCustomEvent(m,!!q.bubbles,!!q.cancelable,q.detail);return r}function c(m){if(Z)return m.ownerDocument!==document?m.ownerDocument:null;var q=m.__importDoc;if(!q&&m.parentNode){q=m.parentNode;if(typeof q.closest==="function")q=q.closest("link[rel=import]");else for(;!h(q)&&(q=q.parentNode););m.__importDoc=q}return q}function d(m){var q=p(document,"link[rel=import]:not([import-dependency])"),
r=q.length;r?v(q,function(u){return g(u,function(){--r===0&&m()})}):m()}function e(m){function q(){document.readyState!=="loading"&&document.body&&(document.removeEventListener("readystatechange",q),m())}document.addEventListener("readystatechange",q);q()}function f(m){e(function(){return d(function(){return m&&m()})})}function g(m,q){if(m.__loaded)q&&q();else if(m.localName==="script"&&!m.src||m.localName==="style"&&!m.firstChild||m.localName==="style"&&m.namespaceURI==="http://www.w3.org/2000/svg")m.__loaded=
!0,q&&q();else{var r=function(u){m.removeEventListener(u.type,r);m.__loaded=!0;q&&q()};m.addEventListener("load",r);m.localName==="style"&&(Ib||Sa)||m.addEventListener("error",r)}}function h(m){return m.nodeType===Node.ELEMENT_NODE&&m.localName==="link"&&m.rel==="import"}function k(){var m=this;this.J={};this.Z=0;this.Ja=new MutationObserver(function(q){return m.Ic(q)});this.Ja.observe(document.head,{childList:!0,subtree:!0});this.loadImports(document)}function l(m){v(p(m,"template"),function(q){v(p(q.content,
'script:not([type]),script[type="application/javascript"],script[type="text/javascript"],script[type="module"]'),function(r){var u=document.createElement("script");v(r.attributes,function(z){return u.setAttribute(z.name,z.value)});u.textContent=r.textContent;r.parentNode.replaceChild(u,r)});l(q.content)})}function p(m,q){return m.childNodes.length?m.querySelectorAll(q):Hg}function v(m,q,r){var u=m?m.length:0,z=r?-1:1;for(r=r?u-1:0;r<u&&r>=0;r+=z)q(m[r],r)}var B=document.createElement("link"),Z="import"in
B,Hg=B.querySelectorAll("*"),Jb=null;"currentScript"in document===!1&&Object.defineProperty(document,"currentScript",{get:function(){return Jb||(document.readyState!=="complete"?document.scripts[document.scripts.length-1]:null)},configurable:!0});var Ig=/(url\()([^)]*)(\))/g,Jg=/(@import[\s]+(?!url\())([^;]*)(;)/g,Kg=/(<link[^>]*)(rel=['|"]?stylesheet['|"]?[^>]*>)/g,Q={Gc:function(m,q){m.href&&m.setAttribute("href",Q.va(m.getAttribute("href"),q));m.src&&m.setAttribute("src",Q.va(m.getAttribute("src"),
q));if(m.localName==="style"){var r=Q.tb(m.textContent,q,Ig);m.textContent=Q.tb(r,q,Jg)}},tb:function(m,q,r){return m.replace(r,function(u,z,w,A){u=w.replace(/["']/g,"");q&&(u=Q.va(u,q));return z+"'"+u+"'"+A})},va:function(m,q){if(Q.xa===void 0){Q.xa=!1;try{var r=new URL("b","http://a");r.pathname="c%20d";Q.xa=r.href==="http://a/c%20d"}catch(u){}}if(Q.xa)return(new URL(m,q)).href;r=Q.Eb;r||(r=document.implementation.createHTMLDocument("temp"),Q.Eb=r,r.Oa=r.createElement("base"),r.head.appendChild(r.Oa),
r.Na=r.createElement("a"));r.Oa.href=q;r.Na.href=m;return r.Na.href||m}},Od={async:!0,load:function(m,q,r){if(m)if(m.match(/^data:/)){m=m.split(",");var u=m[1];u=m[0].indexOf(";base64")>-1?atob(u):decodeURIComponent(u);q(u)}else{var z=new XMLHttpRequest;z.open("GET",m,Od.async);z.onload=function(){var w,A=(w=z.responseURL||z.getResponseHeader("Location"))!=null?w:void 0;A&&A.indexOf("/")===0&&(A=(location.origin||location.protocol+"//"+location.host)+A);w=z.response||z.responseText;z.status===304||
z.status===0||z.status>=200&&z.status<300?q(w,A):r(w)};z.send()}else r("error: href must be specified")}},Ib=/Trident/.test(navigator.userAgent),Sa=/Edge\/\d./i.test(navigator.userAgent);k.prototype.loadImports=function(m){var q=this;m=p(m,"link[rel=import]");v(m,function(r){return q.qb(r)})};k.prototype.qb=function(m){var q=this,r=m.href;if(this.J[r]!==void 0){var u=this.J[r];u&&u.__loaded&&(m.__import=u,this.ob(m))}else this.Z++,this.J[r]="pending",Od.load(r,function(z,w){z=q.Oc(z,w||r);q.J[r]=
z;q.Z--;q.loadImports(z);q.sb()},function(){q.J[r]=null;q.Z--;q.sb()})};k.prototype.Oc=function(m,q){var r=this;if(!m)return document.createDocumentFragment();if(Ib||Sa)m=m.replace(Kg,function(w,A,Ta){return w.indexOf("type=")===-1?A+" type=import-disable "+Ta:w});var u=document.createElement("template");u.innerHTML=m;if(u.content)m=u.content,l(m);else for(m=document.createDocumentFragment();u.firstChild;)m.appendChild(u.firstChild);if(u=m.querySelector("base"))q=Q.va(u.getAttribute("href"),q),u.removeAttribute("href");
u=p(m,'link[rel=import],link[rel=stylesheet][href][type=import-disable],style:not([type]),link[rel=stylesheet][href]:not([type]),script:not([type]),script[type="application/javascript"],script[type="text/javascript"],script[type="module"]');var z=0;v(u,function(w){g(w);Q.Gc(w,q);if(w.localName==="style"&&r.wb(w)){var A=r.Bc(w);g(A);w.parentNode.replaceChild(A,w);w=A}w.setAttribute("import-dependency","");if(w.localName==="script"&&!w.src&&w.textContent){if(w.type==="module")throw Error("Inline module scripts are not supported in HTML Imports.");
w.setAttribute("src","data:text/javascript;charset=utf-8,"+encodeURIComponent(w.textContent+("\n//# sourceURL="+q+(z?"-"+z:"")+".js\n")));w.textContent="";z++}});return m};k.prototype.wb=function(m){return Sa&&m.textContent.indexOf("@import")>-1};k.prototype.Bc=function(m){var q=m.ownerDocument.createElement("style");q.textContent=m.textContent;v(m.attributes,function(r){return q.setAttribute(r.name,r.value)});return q};k.prototype.sb=function(){var m=this;if(!this.Z){this.Ja.disconnect();this.flatten(document);
var q=!1,r=!1,u=function(){r&&q&&(m.loadImports(document),m.Z||(m.Ja.observe(document.head,{childList:!0,subtree:!0}),m.Fc()))};this.dd(function(){r=!0;u()});this.Uc(function(){q=!0;u()})}};k.prototype.flatten=function(m){var q=this;m=p(m,"link[rel=import]");v(m,function(r){var u=q.J[r.href];(r.__import=u)&&u.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&(q.J[r.href]=r,r.readyState="loading",r.__import=r,q.flatten(u),r.appendChild(u))})};k.prototype.Uc=function(m){function q(z){if(z<u){var w=r[z],A=document.createElement("script");
w.removeAttribute("import-dependency");v(w.attributes,function(Ta){return A.setAttribute(Ta.name,Ta.value)});Jb=A;w.parentNode.replaceChild(A,w);g(A,function(){Jb=null;q(z+1)})}else m()}var r=p(document,"script[import-dependency]"),u=r.length;q(0)};k.prototype.dd=function(m){var q=this,r=p(document,"style[import-dependency],link[rel=stylesheet][import-dependency]"),u=r.length;if(u){var z=(Ib||Sa)&&!!document.querySelector("link[rel=stylesheet][href][type=import-disable]");v(r,function(w){z&&q.wb(w)&&
w.ownerDocument.defaultView!==window.top&&(w.__loaded=!0);g(w,function(){w.removeAttribute("import-dependency");--u===0&&m()});if(z&&w.parentNode!==document.head){var A=document.createElement(w.localName);A.__appliedElement=w;A.setAttribute("type","import-placeholder");w.parentNode.insertBefore(A,w.nextSibling);for(A=c(w);A&&c(A);)A=c(A);A.parentNode!==document.head&&(A=null);document.head.insertBefore(w,A);w.removeAttribute("type")}})}else m()};k.prototype.Fc=function(){var m=this,q=p(document,"link[rel=import]");
v(q,function(r){return m.ob(r)},!0)};k.prototype.ob=function(m){m.__loaded||(m.__loaded=!0,m.import&&(m.import.readyState="complete"),m.dispatchEvent(b(m.import?"load":"error",{bubbles:!1,cancelable:!1,detail:void 0})))};k.prototype.Ic=function(m){var q=this;v(m,function(r){return v(r.addedNodes,function(u){u&&u.nodeType===Node.ELEMENT_NODE&&(h(u)?q.qb(u):q.loadImports(u))})})};var Kb=null;if(Z)B=p(document,"link[rel=import]"),v(B,function(m){m.import&&m.import.readyState==="loading"||(m.__loaded=
!0)}),B=function(m){m=m.target;h(m)&&(m.__loaded=!0)},document.addEventListener("load",B,!0),document.addEventListener("error",B,!0);else{var Ca=Object.getOwnPropertyDescriptor(Node.prototype,"baseURI");Object.defineProperty((!Ca||Ca.configurable?Node:Element).prototype,"baseURI",{get:function(){var m=h(this)?this:c(this);return m?m.href:Ca&&Ca.get?Ca.get.call(this):(document.querySelector("base")||window.location).href},configurable:!0,enumerable:!0});Object.defineProperty(HTMLLinkElement.prototype,
"import",{get:function(){return this.__import||null},configurable:!0,enumerable:!0});e(function(){Kb=new k})}f(function(){return document.dispatchEvent(b("HTMLImportsLoaded",{cancelable:!0,bubbles:!0,detail:void 0}))});a.useNative=Z;a.whenReady=f;a.importForElement=c;a.loadImports=function(m){Kb&&Kb.loadImports(m)}})(window.HTMLImports=window.HTMLImports||{});/*

Copyright (c) 2016 The Polymer Project Authors. All rights reserved.
This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
Code distributed by Google as part of the polymer project is also
subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
*/
function Vb(){}Vb.prototype.toJSON=function(){return{}};function E(a){a.__shady||(a.__shady=new Vb);return a.__shady}function F(a){return a&&a.__shady};var G=window.ShadyDOM||{};G.Jc=!(!Element.prototype.attachShadow||!Node.prototype.getRootNode);var Wb=Object.getOwnPropertyDescriptor(Node.prototype,"firstChild");G.j=!!(Wb&&Wb.configurable&&Wb.get);G.inUse=G.force||!G.Jc;G.noPatch=G.noPatch||!1;G.ca=G.preferPerformance;G.La=G.noPatch==="on-demand";var Xb;var Yb=G.querySelectorImplementation;Xb=["native","selectorEngine"].indexOf(Yb)>-1?Yb:void 0;G.querySelectorImplementation=Xb;var Zb=navigator.userAgent.match("Trident");G.zb=Zb;
function $b(){return Document.prototype.msElementsFromPoint?"msElementsFromPoint":"elementsFromPoint"}function ac(a){return(a=F(a))&&a.firstChild!==void 0}function H(a){return a instanceof ShadowRoot}function bc(a){return(a=(a=F(a))&&a.root)&&a.Ya()}var cc=Element.prototype,dc=cc.matches||cc.matchesSelector||cc.mozMatchesSelector||cc.msMatchesSelector||cc.oMatchesSelector||cc.webkitMatchesSelector,ec=document.createTextNode(""),fc=0,hc=[];
(new MutationObserver(function(){for(;hc.length;)try{hc.shift()()}catch(a){throw ec.textContent=fc++,a;}})).observe(ec,{characterData:!0});function ic(a){hc.push(a);ec.textContent=fc++}var jc=document.contains?function(a,b){return a.__shady_native_contains(b)}:function(a,b){return a===b||a.documentElement&&a.documentElement.__shady_native_contains(b)};function kc(a,b){for(;b;){if(b==a)return!0;b=b.__shady_parentNode}return!1}
function lc(a){for(var b=a.length-1;b>=0;b--){var c=a[b],d=c.getAttribute("id")||c.getAttribute("name");d&&d!=="length"&&isNaN(d)&&(a[d]=c)}a.item=function(e){return a[e]};a.namedItem=function(e){if(e!=="length"&&isNaN(e)&&a[e])return a[e];for(var f=t(a),g=f.next();!g.done;g=f.next())if(g=g.value,(g.getAttribute("id")||g.getAttribute("name"))==e)return g;return null};return a}function mc(a){var b=[];for(a=a.__shady_native_firstChild;a;a=a.__shady_native_nextSibling)b.push(a);return b}
function nc(a){var b=[];for(a=a.__shady_firstChild;a;a=a.__shady_nextSibling)b.push(a);return b}function oc(a,b,c){c.configurable=!0;if(c.value)a[b]=c.value;else try{Object.defineProperty(a,b,c)}catch(d){}}function I(a,b,c,d){c=c===void 0?"":c;for(var e in b)d&&d.indexOf(e)>=0||oc(a,c+e,b[e])}function pc(a,b){for(var c in b)c in a&&oc(a,c,b[c])}function J(a){var b={};Object.getOwnPropertyNames(a).forEach(function(c){b[c]=Object.getOwnPropertyDescriptor(a,c)});return b}
function qc(a,b){for(var c=Object.getOwnPropertyNames(b),d=0,e;d<c.length;d++)e=c[d],a[e]=b[e]}function rc(a){return a instanceof Node?a:document.createTextNode(""+a)}function sc(){var a=y.apply(0,arguments);if(a.length===1)return rc(a[0]);var b=document.createDocumentFragment();a=t(a);for(var c=a.next();!c.done;c=a.next())b.appendChild(rc(c.value));return b}
function tc(a){var b;for(b=b===void 0?1:b;b>0;b--)a=a.reduce(function(c,d){Array.isArray(d)?c.push.apply(c,x(d)):c.push(d);return c},[]);return a}function uc(a){var b=[],c=new Set;a=t(a);for(var d=a.next();!d.done;d=a.next())d=d.value,c.has(d)||(b.push(d),c.add(d));return b};var vc=[],wc;function xc(a){wc||(wc=!0,ic(yc));vc.push(a)}function yc(){wc=!1;for(var a=!!vc.length;vc.length;)vc.shift()();return a}yc.list=vc;function zc(){this.ma=!1;this.addedNodes=[];this.removedNodes=[];this.ra=new Set}function Ac(a){a.ma||(a.ma=!0,ic(function(){a.flush()}))}zc.prototype.flush=function(){if(this.ma){this.ma=!1;var a=this.takeRecords();a.length&&this.ra.forEach(function(b){b(a)})}};zc.prototype.takeRecords=function(){if(this.addedNodes.length||this.removedNodes.length){var a=[{addedNodes:this.addedNodes,removedNodes:this.removedNodes}];this.addedNodes=[];this.removedNodes=[];return a}return[]};
function Bc(a,b){var c=E(a);c.ba||(c.ba=new zc);c.ba.ra.add(b);var d=c.ba;return{Hb:b,P:d,ec:a,takeRecords:function(){return d.takeRecords()}}}function Cc(a){var b=a&&a.P;b&&(b.ra.delete(a.Hb),b.ra.size||(E(a.ec).ba=null))}
function Dc(a,b){var c=b.getRootNode();return a.map(function(d){var e=c===d.target.getRootNode();if(e&&d.addedNodes){if(e=[].slice.call(d.addedNodes).filter(function(f){return c===f.getRootNode()}),e.length)return d=Object.create(d),Object.defineProperty(d,"addedNodes",{value:e,configurable:!0}),d}else if(e)return d}).filter(function(d){return d})};var Ec=/[&\u00A0"]/g,Fc=/[&\u00A0<>]/g;function Gc(a){switch(a){case "&":return"&amp;";case "<":return"&lt;";case ">":return"&gt;";case '"':return"&quot;";case "\u00a0":return"&nbsp;"}}function Hc(a){for(var b={},c=0;c<a.length;c++)b[a[c]]=!0;return b}var Ic=Hc("area base br col command embed hr img input keygen link meta param source track wbr".split(" ")),Jc=Hc("style script xmp iframe noembed noframes plaintext noscript".split(" "));
function Kc(a,b){a.localName==="template"&&(a=a.content);for(var c="",d=b?b(a):a.childNodes,e=0,f=d.length,g=void 0;e<f&&(g=d[e]);e++){a:{var h=g;var k=a,l=b;switch(h.nodeType){case Node.ELEMENT_NODE:k=h.localName;for(var p="<"+k,v=h.attributes,B=0,Z;Z=v[B];B++)p+=" "+Z.name+'="'+Z.value.replace(Ec,Gc)+'"';p+=">";h=Ic[k]?p:p+Kc(h,l)+"</"+k+">";break a;case Node.TEXT_NODE:h=h.data;h=k&&Jc[k.localName]?h:h.replace(Fc,Gc);break a;case Node.COMMENT_NODE:h="\x3c!--"+h.data+"--\x3e";break a;default:throw window.console.error(h),
Error("not implemented");}h=void 0}c+=h}return c};var Lc=G.j,Mc={querySelector:function(a){return this.__shady_native_querySelector(a)},querySelectorAll:function(a){return this.__shady_native_querySelectorAll(a)}},Nc={};function Oc(a){Nc[a]=function(b){return b["__shady_native_"+a]}}function Pc(a,b){I(a,b,"__shady_native_");for(var c in b)Oc(c)}function K(a,b){b=b===void 0?[]:b;for(var c=0;c<b.length;c++){var d=b[c],e=Object.getOwnPropertyDescriptor(a,d);e&&(Object.defineProperty(a,"__shady_native_"+d,e),e.value?Mc[d]||(Mc[d]=e.value):Oc(d))}}
var L=document.createTreeWalker(document,NodeFilter.SHOW_ALL,null,!1),M=document.createTreeWalker(document,NodeFilter.SHOW_ELEMENT,null,!1),Qc=document.implementation.createHTMLDocument("inert");function Rc(a){for(var b;b=a.__shady_native_firstChild;)a.__shady_native_removeChild(b)}var Sc=["firstElementChild","lastElementChild","children","childElementCount"],Tc=["querySelector","querySelectorAll","append","prepend","replaceChildren"];
function Uc(){var a=["dispatchEvent","addEventListener","removeEventListener"];window.EventTarget?(K(window.EventTarget.prototype,a),window.__shady_native_addEventListener===void 0&&K(Window.prototype,a)):(K(Node.prototype,a),K(Window.prototype,a),K(XMLHttpRequest.prototype,a));Lc?K(Node.prototype,"parentNode firstChild lastChild previousSibling nextSibling childNodes parentElement textContent".split(" ")):Pc(Node.prototype,{parentNode:{get:function(){L.currentNode=this;return L.parentNode()}},firstChild:{get:function(){L.currentNode=
this;return L.firstChild()}},lastChild:{get:function(){L.currentNode=this;return L.lastChild()}},previousSibling:{get:function(){L.currentNode=this;return L.previousSibling()}},nextSibling:{get:function(){L.currentNode=this;return L.nextSibling()}},childNodes:{get:function(){var b=[];L.currentNode=this;for(var c=L.firstChild();c;)b.push(c),c=L.nextSibling();return b}},parentElement:{get:function(){M.currentNode=this;return M.parentNode()}},textContent:{get:function(){switch(this.nodeType){case Node.ELEMENT_NODE:case Node.DOCUMENT_FRAGMENT_NODE:for(var b=
document.createTreeWalker(this,NodeFilter.SHOW_TEXT,null,!1),c="",d;d=b.nextNode();)c+=d.nodeValue;return c;default:return this.nodeValue}},set:function(b){if(typeof b==="undefined"||b===null)b="";switch(this.nodeType){case Node.ELEMENT_NODE:case Node.DOCUMENT_FRAGMENT_NODE:Rc(this);(b.length>0||this.nodeType===Node.ELEMENT_NODE)&&this.__shady_native_insertBefore(document.createTextNode(b),void 0);break;default:this.nodeValue=b}}}});K(Node.prototype,"appendChild insertBefore removeChild replaceChild cloneNode contains".split(" "));
K(HTMLElement.prototype,["parentElement","contains"]);a={firstElementChild:{get:function(){M.currentNode=this;return M.firstChild()}},lastElementChild:{get:function(){M.currentNode=this;return M.lastChild()}},children:{get:function(){var b=[];M.currentNode=this;for(var c=M.firstChild();c;)b.push(c),c=M.nextSibling();return lc(b)}},childElementCount:{get:function(){return this.children?this.children.length:0}}};Lc?(K(Element.prototype,Sc),K(Element.prototype,["previousElementSibling","nextElementSibling",
"innerHTML","className"]),K(HTMLElement.prototype,["children","innerHTML","className"])):(Pc(Element.prototype,a),Pc(Element.prototype,{previousElementSibling:{get:function(){M.currentNode=this;return M.previousSibling()}},nextElementSibling:{get:function(){M.currentNode=this;return M.nextSibling()}},innerHTML:{get:function(){return Kc(this,mc)},set:function(b){var c=this.localName==="template"?this.content:this;Rc(c);var d=this.localName||"div";d=this.namespaceURI&&this.namespaceURI!==Qc.namespaceURI?
Qc.createElementNS(this.namespaceURI,d):Qc.createElement(d);d.innerHTML=b;for(b=this.localName==="template"?d.content:d;d=b.__shady_native_firstChild;)c.__shady_native_insertBefore(d,void 0)}},className:{get:function(){return this.getAttribute("class")||""},set:function(b){this.setAttribute("class",b)}}}));K(Element.prototype,"setAttribute getAttribute hasAttribute removeAttribute toggleAttribute focus blur".split(" "));K(Element.prototype,Tc);K(HTMLElement.prototype,["focus","blur"]);window.HTMLTemplateElement&&
K(window.HTMLTemplateElement.prototype,["innerHTML"]);Lc?K(DocumentFragment.prototype,Sc):Pc(DocumentFragment.prototype,a);K(DocumentFragment.prototype,Tc);Lc?(K(Document.prototype,Sc),K(Document.prototype,["activeElement"])):Pc(Document.prototype,a);K(Document.prototype,["importNode","getElementById","elementFromPoint",$b()]);K(Document.prototype,Tc)};var Vc=J({get childNodes(){return this.__shady_childNodes},get firstChild(){return this.__shady_firstChild},get lastChild(){return this.__shady_lastChild},get childElementCount(){return this.__shady_childElementCount},get children(){return this.__shady_children},get firstElementChild(){return this.__shady_firstElementChild},get lastElementChild(){return this.__shady_lastElementChild},get shadowRoot(){return this.__shady_shadowRoot}}),Wc=J({get textContent(){return this.__shady_textContent},set textContent(a){this.__shady_textContent=
a},get innerHTML(){return this.__shady_innerHTML},set innerHTML(a){this.__shady_innerHTML=a}}),Xc=J({get parentElement(){return this.__shady_parentElement},get parentNode(){return this.__shady_parentNode},get nextSibling(){return this.__shady_nextSibling},get previousSibling(){return this.__shady_previousSibling},get nextElementSibling(){return this.__shady_nextElementSibling},get previousElementSibling(){return this.__shady_previousElementSibling},get className(){return this.__shady_className},set className(a){this.__shady_className=
a}});function Yc(a){for(var b in a){var c=a[b];c&&(c.enumerable=!1)}}Yc(Vc);Yc(Wc);Yc(Xc);var Zc=G.j||G.noPatch===!0,$c=Zc?function(){}:function(a){var b=E(a);b.Cb||(b.Cb=!0,pc(a,Xc))},ad=Zc?function(){}:function(a){var b=E(a);b.Bb||(b.Bb=!0,pc(a,Vc),window.customElements&&window.customElements.polyfillWrapFlushCallback&&!G.noPatch||pc(a,Wc))};var bd="__eventWrappers"+Date.now(),cd=function(){var a=Object.getOwnPropertyDescriptor(Event.prototype,"composed");return a?function(b){return a.get.call(b)}:null}(),dd=function(){function a(){}var b=!1,c={get capture(){b=!0;return!1}};window.addEventListener("test",a,c);window.removeEventListener("test",a,c);return b}();function ed(a){if(a===null||typeof a!=="object"&&typeof a!=="function"){var b=!!a;var c=!1}else{b=!!a.capture;c=!!a.once;var d=a.L}return{ub:d,capture:b,once:c,rb:dd?a:b}}
var fd={blur:!0,focus:!0,focusin:!0,focusout:!0,click:!0,dblclick:!0,mousedown:!0,mouseenter:!0,mouseleave:!0,mousemove:!0,mouseout:!0,mouseover:!0,mouseup:!0,wheel:!0,beforeinput:!0,input:!0,keydown:!0,keyup:!0,compositionstart:!0,compositionupdate:!0,compositionend:!0,touchstart:!0,touchend:!0,touchmove:!0,touchcancel:!0,pointerover:!0,pointerenter:!0,pointerdown:!0,pointermove:!0,pointerup:!0,pointercancel:!0,pointerout:!0,pointerleave:!0,gotpointercapture:!0,lostpointercapture:!0,dragstart:!0,
drag:!0,dragenter:!0,dragleave:!0,dragover:!0,drop:!0,dragend:!0,DOMActivate:!0,DOMFocusIn:!0,DOMFocusOut:!0,keypress:!0},gd={DOMAttrModified:!0,DOMAttributeNameChanged:!0,DOMCharacterDataModified:!0,DOMElementNameChanged:!0,DOMNodeInserted:!0,DOMNodeInsertedIntoDocument:!0,DOMNodeRemoved:!0,DOMNodeRemovedFromDocument:!0,DOMSubtreeModified:!0};function hd(a){return a instanceof Node?a.__shady_getRootNode():a}
function id(a,b){var c=[],d=a;for(a=hd(a);d;)c.push(d),d=d.__shady_assignedSlot?d.__shady_assignedSlot:d.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&d.host&&(b||d!==a)?d.host:d.__shady_parentNode;c[c.length-1]===document&&c.push(window);return c}function jd(a){a.__composedPath||(a.__composedPath=id(a.target,!0));return a.__composedPath}function kd(a,b){if(!H)return a;a=id(a,!0);for(var c=0,d,e=void 0,f,g=void 0;c<b.length;c++)if(d=b[c],f=hd(d),f!==e&&(g=a.indexOf(f),e=f),!H(f)||g>-1)return d}
var ld={get composed(){this.__composed===void 0&&(cd?this.__composed=this.type==="focusin"||this.type==="focusout"||cd(this):this.isTrusted!==!1&&(this.__composed=fd[this.type]));return this.__composed||!1},composedPath:function(){this.__composedPath||(this.__composedPath=id(this.__target,this.composed));return this.__composedPath},get target(){return kd(this.currentTarget||this.__previousCurrentTarget,this.composedPath())},get relatedTarget(){if(!this.__relatedTarget)return null;this.__relatedTargetComposedPath||
(this.__relatedTargetComposedPath=id(this.__relatedTarget,!0));return kd(this.currentTarget||this.__previousCurrentTarget,this.__relatedTargetComposedPath)},stopPropagation:function(){Event.prototype.stopPropagation.call(this);this.wa=!0},stopImmediatePropagation:function(){Event.prototype.stopImmediatePropagation.call(this);this.wa=this.__immediatePropagationStopped=!0}},md=G.j&&Object.getOwnPropertyDescriptor(Event.prototype,"eventPhase");
md&&(Object.defineProperty(ld,"eventPhase",{get:function(){return this.currentTarget===this.target?Event.AT_TARGET:this.__shady_native_eventPhase},enumerable:!0,configurable:!0}),Object.defineProperty(ld,"__shady_native_eventPhase",md));function nd(a){function b(c,d){c=new a(c,d);c.__composed=d&&!!d.composed;return c}b.__proto__=a;b.prototype=a.prototype;return b}var od={focus:!0,blur:!0};function pd(a){return a.__target!==a.target||a.__relatedTarget!==a.relatedTarget}
function qd(a,b,c){if(c=b.__handlers&&b.__handlers[a.type]&&b.__handlers[a.type][c])for(var d=0,e;(e=c[d])&&(!pd(a)||a.target!==a.relatedTarget)&&(e.call(b,a),!a.__immediatePropagationStopped);d++);}var rd=(new Event("e")).hasOwnProperty("currentTarget");
function sd(a){a=rd?Object.create(a):a;var b=a.composedPath(),c=b.map(function(p){return kd(p,b)}),d=a.bubbles,e=Object.getOwnPropertyDescriptor(a,"currentTarget");Object.defineProperty(a,"currentTarget",{configurable:!0,enumerable:!0,get:function(){return k}});var f=Event.CAPTURING_PHASE,g=Object.getOwnPropertyDescriptor(a,"eventPhase");Object.defineProperty(a,"eventPhase",{configurable:!0,enumerable:!0,get:function(){return f}});try{for(var h=b.length-1;h>=0;h--){var k=b[h];f=k===c[h]?Event.AT_TARGET:
Event.CAPTURING_PHASE;qd(a,k,"capture");if(a.wa)return}for(h=0;h<b.length;h++){k=b[h];var l=k===c[h];if(l||d)if(f=l?Event.AT_TARGET:Event.BUBBLING_PHASE,qd(a,k,"bubble"),a.wa)break}}finally{rd||(e?Object.defineProperty(a,"currentTarget",e):delete a.currentTarget,g?Object.defineProperty(a,"eventPhase",g):delete a.eventPhase)}}function td(a,b,c,d){for(var e=0;e<a.length;e++){var f=a[e],g=f.type,h=f.capture;if(b===f.node&&c===g&&d===h)return e}return-1}
function ud(a){yc();return!G.ca&&this instanceof Node&&!jc(document,this)?(a.__target||vd(a,this),sd(a)):this.__shady_native_dispatchEvent(a)}
function wd(a,b,c){var d=this,e=ed(c),f=e.capture,g=e.once,h=e.ub;e=e.rb;if(b){var k=typeof b;if(k==="function"||k==="object")if(k!=="object"||b.handleEvent&&typeof b.handleEvent==="function"){if(gd[a])return this.__shady_native_addEventListener(a,b,e);var l=h||this;if(h=b[bd]){if(td(h,l,a,f)>-1)return}else b[bd]=[];h=function(p){g&&d.__shady_removeEventListener(a,b,c);p.__target||vd(p);if(l!==d){var v=Object.getOwnPropertyDescriptor(p,"currentTarget");Object.defineProperty(p,"currentTarget",{get:function(){return l},
configurable:!0});var B=Object.getOwnPropertyDescriptor(p,"eventPhase");Object.defineProperty(p,"eventPhase",{configurable:!0,enumerable:!0,get:function(){return f?Event.CAPTURING_PHASE:Event.BUBBLING_PHASE}})}p.__previousCurrentTarget=p.currentTarget;if(!H(l)&&l.localName!=="slot"||p.composedPath().indexOf(l)!=-1)if(p.composed||p.composedPath().indexOf(l)>-1)if(pd(p)&&p.target===p.relatedTarget)p.eventPhase===Event.BUBBLING_PHASE&&p.stopImmediatePropagation();else if(p.eventPhase===Event.CAPTURING_PHASE||
p.bubbles||p.target===l||l instanceof Window){var Z=k==="function"?b.call(l,p):b.handleEvent&&b.handleEvent(p);l!==d&&(v?(Object.defineProperty(p,"currentTarget",v),v=null):delete p.currentTarget,B?(Object.defineProperty(p,"eventPhase",B),B=null):delete p.eventPhase);return Z}};b[bd].push({node:l,type:a,capture:f,ed:h});this.__handlers=this.__handlers||{};this.__handlers[a]=this.__handlers[a]||{capture:[],bubble:[]};this.__handlers[a][f?"capture":"bubble"].push(h);od[a]||this.__shady_native_addEventListener(a,
h,e)}}}function xd(a,b,c){if(b){var d=ed(c);c=d.capture;var e=d.ub;d=d.rb;if(gd[a])return this.__shady_native_removeEventListener(a,b,d);var f=e||this;e=void 0;var g=null;try{g=b[bd]}catch(h){}g&&(f=td(g,f,a,c),f>-1&&(e=g.splice(f,1)[0].ed,g.length||(b[bd]=void 0)));this.__shady_native_removeEventListener(a,e||b,d);e&&this.__handlers&&this.__handlers[a]&&(a=this.__handlers[a][c?"capture":"bubble"],b=a.indexOf(e),b>-1&&a.splice(b,1))}}
function yd(){for(var a in od)window.__shady_native_addEventListener(a,function(b){b.__target||(vd(b),sd(b))},!0)}var zd=J(ld);function vd(a,b){b=b===void 0?a.target:b;a.__target=b;a.__relatedTarget=a.relatedTarget;if(G.j){b=Object.getPrototypeOf(a);if(!b.hasOwnProperty("__shady_patchedProto")){var c=Object.create(b);c.__shady_sourceProto=b;I(c,zd);b.__shady_patchedProto=c}a.__proto__=b.__shady_patchedProto}else I(a,zd)}var Ad=nd(Event),Bd=nd(CustomEvent),Cd=nd(MouseEvent);
function Dd(){if(!cd&&Object.getOwnPropertyDescriptor(Event.prototype,"isTrusted")){var a=function(){var b=new MouseEvent("click",{bubbles:!0,cancelable:!0,composed:!0});this.__shady_dispatchEvent(b)};Element.prototype.click?Element.prototype.click=a:HTMLElement.prototype.click&&(HTMLElement.prototype.click=a)}}
var Ed=Object.getOwnPropertyNames(Element.prototype).filter(function(a){return a.substring(0,2)==="on"}),Fd=Object.getOwnPropertyNames(HTMLElement.prototype).filter(function(a){return a.substring(0,2)==="on"});function Gd(a){return{set:function(b){var c=E(this),d=a.substring(2);c.K||(c.K={});c.K[a]&&this.removeEventListener(d,c.K[a]);this.__shady_addEventListener(d,b);c.K[a]=b},get:function(){var b=F(this);return b&&b.K&&b.K[a]},configurable:!0}};function Hd(a,b){return{index:a,da:[],qa:b}}
function Id(a,b,c,d){var e=0,f=0,g=0,h=0,k=Math.min(b-e,d-f);if(e==0&&f==0)a:{for(g=0;g<k;g++)if(a[g]!==c[g])break a;g=k}if(b==a.length&&d==c.length){h=a.length;for(var l=c.length,p=0;p<k-g&&Jd(a[--h],c[--l]);)p++;h=p}e+=g;f+=g;b-=h;d-=h;if(b-e==0&&d-f==0)return[];if(e==b){for(b=Hd(e,0);f<d;)b.da.push(c[f++]);return[b]}if(f==d)return[Hd(e,b-e)];k=e;g=f;d=d-g+1;h=b-k+1;b=Array(d);for(l=0;l<d;l++)b[l]=Array(h),b[l][0]=l;for(l=0;l<h;l++)b[0][l]=l;for(l=1;l<d;l++)for(p=1;p<h;p++)if(a[k+p-1]===c[g+l-1])b[l][p]=
b[l-1][p-1];else{var v=b[l-1][p]+1,B=b[l][p-1]+1;b[l][p]=v<B?v:B}k=b.length-1;g=b[0].length-1;d=b[k][g];for(a=[];k>0||g>0;)k==0?(a.push(2),g--):g==0?(a.push(3),k--):(h=b[k-1][g-1],l=b[k-1][g],p=b[k][g-1],v=l<p?l<h?l:h:p<h?p:h,v==h?(h==d?a.push(0):(a.push(1),d=h),k--,g--):v==l?(a.push(3),k--,d=l):(a.push(2),g--,d=p));a.reverse();b=void 0;k=[];for(g=0;g<a.length;g++)switch(a[g]){case 0:b&&(k.push(b),b=void 0);e++;f++;break;case 1:b||(b=Hd(e,0));b.qa++;e++;b.da.push(c[f]);f++;break;case 2:b||(b=Hd(e,
0));b.qa++;e++;break;case 3:b||(b=Hd(e,0)),b.da.push(c[f]),f++}b&&k.push(b);return k}function Jd(a,b){return a===b};var Kd=J({dispatchEvent:ud,addEventListener:wd,removeEventListener:xd});var Ld=null;function Md(){Ld||(Ld=window.ShadyCSS&&window.ShadyCSS.ScopingShim);return Ld||null}function Nd(a,b,c){var d=Md();return d&&b==="class"?(d.setElementClass(a,c),!0):!1}function Pd(a,b){var c=Md();c&&c.unscopeNode(a,b)}function Qd(a,b){var c=Md();if(!c)return!0;if(a.nodeType===Node.DOCUMENT_FRAGMENT_NODE){c=!0;for(a=a.__shady_firstChild;a;a=a.__shady_nextSibling)c=c&&Qd(a,b);return c}return a.nodeType!==Node.ELEMENT_NODE?!0:c.currentScopeForNode(a)===b}
function Rd(a){if(a.nodeType!==Node.ELEMENT_NODE)return"";var b=Md();return b?b.currentScopeForNode(a):""}function Sd(a,b){if(a)for(a.nodeType===Node.ELEMENT_NODE&&b(a),a=a.__shady_firstChild;a;a=a.__shady_nextSibling)a.nodeType===Node.ELEMENT_NODE&&Sd(a,b)};var Td=window.document,Ud=G.ca,Vd=Object.getOwnPropertyDescriptor(Node.prototype,"isConnected"),Wd=Vd&&Vd.get;function Xd(a){for(var b;b=a.__shady_firstChild;)a.__shady_removeChild(b)}function Yd(a){var b=F(a);if(b&&b.ua!==void 0)for(b=a.__shady_firstChild;b;b=b.__shady_nextSibling)Yd(b);if(a=F(a))a.ua=void 0}function Zd(a){var b=a;if(a&&a.localName==="slot"){var c=F(a);(c=c&&c.T)&&(b=c.length?c[0]:Zd(a.__shady_nextSibling))}return b}
function $d(a,b,c){if(a=(a=F(a))&&a.ba){if(b)if(b.nodeType===Node.DOCUMENT_FRAGMENT_NODE)for(var d=0,e=b.childNodes.length;d<e;d++)a.addedNodes.push(b.childNodes[d]);else a.addedNodes.push(b);c&&a.removedNodes.push(c);Ac(a)}}
var de=J({get parentNode(){var a=F(this);a=a&&a.parentNode;return a!==void 0?a:this.__shady_native_parentNode},get firstChild(){var a=F(this);a=a&&a.firstChild;return a!==void 0?a:this.__shady_native_firstChild},get lastChild(){var a=F(this);a=a&&a.lastChild;return a!==void 0?a:this.__shady_native_lastChild},get nextSibling(){var a=F(this);a=a&&a.nextSibling;return a!==void 0?a:this.__shady_native_nextSibling},get previousSibling(){var a=F(this);a=a&&a.previousSibling;return a!==void 0?a:this.__shady_native_previousSibling},
get childNodes(){if(ac(this)){var a=F(this);if(!a.childNodes){a.childNodes=[];for(var b=this.__shady_firstChild;b;b=b.__shady_nextSibling)a.childNodes.push(b)}var c=a.childNodes}else c=this.__shady_native_childNodes;c.item=function(d){return c[d]};return c},get parentElement(){var a=F(this);(a=a&&a.parentNode)&&a.nodeType!==Node.ELEMENT_NODE&&(a=null);return a!==void 0?a:this.__shady_native_parentElement},get isConnected(){if(Wd&&Wd.call(this))return!0;if(this.nodeType==Node.DOCUMENT_FRAGMENT_NODE)return!1;
var a=this.ownerDocument;if(a===null||jc(a,this))return!0;for(a=this;a&&!(a instanceof Document);)a=a.__shady_parentNode||(H(a)?a.host:void 0);return!!(a&&a instanceof Document)},get textContent(){if(ac(this)){for(var a=[],b=this.__shady_firstChild;b;b=b.__shady_nextSibling)b.nodeType!==Node.COMMENT_NODE&&a.push(b.__shady_textContent);return a.join("")}return this.__shady_native_textContent},set textContent(a){if(typeof a==="undefined"||a===null)a="";switch(this.nodeType){case Node.ELEMENT_NODE:case Node.DOCUMENT_FRAGMENT_NODE:if(!ac(this)&&
G.j){var b=this.__shady_firstChild;(b!=this.__shady_lastChild||b&&b.nodeType!=Node.TEXT_NODE)&&Xd(this);this.__shady_native_textContent=a}else Xd(this),(a.length>0||this.nodeType===Node.ELEMENT_NODE)&&this.__shady_insertBefore(document.createTextNode(a));break;default:this.nodeValue=a}},insertBefore:function(a,b){if(this.ownerDocument!==Td&&a.ownerDocument!==Td)return this.__shady_native_insertBefore(a,b),a;if(a===this)throw Error("Failed to execute 'appendChild' on 'Node': The new child element contains the parent.");
if(b){var c=F(b);c=c&&c.parentNode;if(c!==void 0&&c!==this||c===void 0&&b.__shady_native_parentNode!==this)throw Error("Failed to execute 'insertBefore' on 'Node': The node before which the new node is to be inserted is not a child of this node.");}if(b===a)return a;$d(this,a);var d=[],e=(c=ae(this))?c.host.localName:Rd(this),f=a.__shady_parentNode;if(f){var g=Rd(a);var h=!!c||!ae(a)||Ud&&this.__noInsertionPoint!==void 0;f.__shady_removeChild(a,h)}f=!0;var k=(!Ud||a.__noInsertionPoint===void 0&&this.__noInsertionPoint===
void 0)&&!Qd(a,e),l=c&&!a.__noInsertionPoint&&(!Ud||a.nodeType===Node.DOCUMENT_FRAGMENT_NODE);if(l||k)k&&(g=g||Rd(a)),Sd(a,function(p){l&&p.localName==="slot"&&d.push(p);if(k){var v=g;Md()&&(v&&Pd(p,v),(v=Md())&&v.scopeNode(p,e))}});d.length&&(c.Qa(d),c.v());ac(this)&&(be(a,this,b),h=F(this),h.root?(f=!1,bc(this)&&h.root.v()):c&&this.localName==="slot"&&(f=!1,c.v()));f?(c=H(this)?this.host:this,b?(b=Zd(b),c.__shady_native_insertBefore(a,b)):c.__shady_native_appendChild(a)):a.ownerDocument!==this.ownerDocument&&
this.ownerDocument.adoptNode(a);return a},appendChild:function(a){if(this!=a||!H(a))return this.__shady_insertBefore(a)},removeChild:function(a,b){b=b===void 0?!1:b;if(this.ownerDocument!==Td)return this.__shady_native_removeChild(a);if(a.__shady_parentNode!==this)throw Error("The node to be removed is not a child of this node: "+a);$d(this,null,a);var c=ae(a),d=c&&c.jc(a),e=F(this);if(ac(this)&&(ce(a,this),bc(this))){e.root.v();var f=!0}if(Md()&&!b&&c&&a.nodeType!==Node.TEXT_NODE){var g=Rd(a);Sd(a,
function(h){Pd(h,g)})}Yd(a);c&&((b=this.localName==="slot")&&(f=!0),(d||b)&&c.v());f||(f=H(this)?this.host:this,(!e.root&&a.localName!=="slot"||f===a.__shady_native_parentNode)&&f.__shady_native_removeChild(a));return a},replaceChild:function(a,b){this.__shady_insertBefore(a,b);this.__shady_removeChild(b);return a},cloneNode:function(a){if(this.localName=="template")return this.__shady_native_cloneNode(a);var b=this.__shady_native_cloneNode(!1);if(a&&b.nodeType!==Node.ATTRIBUTE_NODE){a=this.__shady_firstChild;
for(var c;a;a=a.__shady_nextSibling)c=a.__shady_cloneNode(!0),b.__shady_appendChild(c)}return b},getRootNode:function(a){if(this&&this.nodeType){var b=E(this),c=b.ua;c===void 0&&(H(this)?(c=this,b.ua=c):(c=(c=this.__shady_parentNode)?c.__shady_getRootNode(a):this,document.documentElement.__shady_native_contains(this)&&(b.ua=c)));return c}},contains:function(a){return kc(this,a)}});var ee=J({get assignedSlot(){var a=this.__shady_parentNode;(a=a&&a.__shady_shadowRoot)&&a.la();return(a=F(this))&&a.assignedSlot||null}});/*

 Copyright (c) 2022 The Polymer Project Authors
 SPDX-License-Identifier: BSD-3-Clause
*/
var fe=new Map;[["(",{end:")",ta:!0}],["[",{end:"]",ta:!0}],['"',{end:'"',ta:!1}],["'",{end:"'",ta:!1}]].forEach(function(a){var b=t(a);a=b.next().value;b=b.next().value;fe.set(a,b)});function ge(a,b,c,d){for(d=d===void 0?!0:d;b<a.length;b++)if(a[b]==="\\"&&b<a.length-1&&a[b+1]!=="\n")b++;else{if(c.indexOf(a[b])!==-1)return b;if(d&&fe.has(a[b])){var e=fe.get(a[b]);b=ge(a,b+1,[e.end],e.ta)}}return a.length}
function he(a){function b(){if(d.length>0){for(;d[d.length-1]===" ";)d.pop();c.push({nb:d.filter(function(k,l){return l%2===0}),Dc:d.filter(function(k,l){return l%2===1})});d.length=0}}for(var c=[],d=[],e=0;e<a.length;){var f=d[d.length-1],g=ge(a,e,[","," ",">","+","~"]),h=g===e?a[e]:a.substring(e,g);if(h===",")b();else if([void 0," ",">","+","~"].indexOf(f)===-1||h!==" ")f===" "&&[">","+","~"].indexOf(h)!==-1?d[d.length-1]=h:d.push(h);e=g+(g===e?1:0)}b();return c};function ie(a,b,c){var d=[];je(a,b,c,d);return d}function je(a,b,c,d){for(a=a.__shady_firstChild;a;a=a.__shady_nextSibling){var e;if(e=a.nodeType===Node.ELEMENT_NODE){e=a;var f=b,g=c,h=d,k=f(e);k&&h.push(e);g&&g(k)?e=k:(je(e,f,g,h),e=void 0)}if(e)break}}
var ke={get firstElementChild(){var a=F(this);if(a&&a.firstChild!==void 0){for(a=this.__shady_firstChild;a&&a.nodeType!==Node.ELEMENT_NODE;)a=a.__shady_nextSibling;return a}return this.__shady_native_firstElementChild},get lastElementChild(){var a=F(this);if(a&&a.lastChild!==void 0){for(a=this.__shady_lastChild;a&&a.nodeType!==Node.ELEMENT_NODE;)a=a.__shady_previousSibling;return a}return this.__shady_native_lastElementChild},get children(){return ac(this)?lc(Array.prototype.filter.call(nc(this),
function(a){return a.nodeType===Node.ELEMENT_NODE})):this.__shady_native_children},get childElementCount(){var a=this.__shady_children;return a?a.length:0}},le=J((ke.append=function(){this.__shady_insertBefore(sc.apply(null,x(y.apply(0,arguments))),null)},ke.prepend=function(){this.__shady_insertBefore(sc.apply(null,x(y.apply(0,arguments))),this.__shady_firstChild)},ke.replaceChildren=function(){for(var a=y.apply(0,arguments),b;(b=this.__shady_firstChild)!==null;)this.__shady_removeChild(b);this.__shady_insertBefore(sc.apply(null,
x(a)),null)},ke));
function me(a,b){function c(e,f){return(e===a||f.indexOf(":scope")===-1)&&dc.call(e,f)}var d=he(b);if(d.length<1)return[];for(b=tc(ie(a,function(){return!0}).map(function(e){return tc(d.map(function(f){var g=f.nb,h=g.length-1;return c(e,g[h])?{target:e,Y:f,aa:e,index:h}:[]}))}));b.some(function(e){return e.index>0});)b=tc(b.map(function(e){if(e.index<=0)return e;var f=e.target,g=e.aa,h=e.Y;e=e.index-1;var k=h.Dc[e],l=h.nb[e];if(k===" "){k=[];for(g=g.__shady_parentElement;g;g=g.__shady_parentElement)c(g,l)&&
k.push({target:f,Y:h,aa:g,index:e});return k}if(k===">")return g=g.__shady_parentElement,c(g,l)?{target:f,Y:h,aa:g,index:e}:[];if(k==="+")return(g=g.__shady_previousElementSibling)&&c(g,l)?{target:f,Y:h,aa:g,index:e}:[];if(k==="~"){k=[];for(g=g.__shady_previousElementSibling;g;g=g.__shady_previousElementSibling)c(g,l)&&k.push({target:f,Y:h,aa:g,index:e});return k}throw Error("Unrecognized combinator: '"+k+"'.");}));return uc(b.map(function(e){return e.target}))}
var ne=G.querySelectorImplementation,oe=J({querySelector:function(a){if(ne==="native"){var b=Array.prototype.slice.call((this instanceof ShadowRoot?this.host:this).__shady_native_querySelectorAll(a)),c=this.__shady_getRootNode();b=t(b);for(var d=b.next();!d.done;d=b.next())if(d=d.value,d.__shady_getRootNode()==c)return d;return null}if(ne==="selectorEngine")return me(this,a)[0]||null;if(ne===void 0)return ie(this,function(e){return dc.call(e,a)},function(e){return!!e})[0]||null;throw Error("Unrecognized value of ShadyDOM.querySelectorImplementation: '"+
(ne+"'"));},querySelectorAll:function(a,b){if(b||ne==="native"){b=Array.prototype.slice.call((this instanceof ShadowRoot?this.host:this).__shady_native_querySelectorAll(a));var c=this.__shady_getRootNode();return lc(b.filter(function(d){return d.__shady_getRootNode()==c}))}if(ne==="selectorEngine")return lc(me(this,a));if(ne===void 0)return lc(ie(this,function(d){return dc.call(d,a)}));throw Error("Unrecognized value of ShadyDOM.querySelectorImplementation: '"+(ne+"'"));}}),pe=G.ca&&!G.noPatch?qc({},
le):le;qc(le,oe);/*

Copyright (c) 2020 The Polymer Project Authors. All rights reserved.
This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
Code distributed by Google as part of the polymer project is also
subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
*/
var qe=J({after:function(){var a=this.__shady_parentNode;if(a!==null){var b=this.__shady_nextSibling;a.__shady_insertBefore(sc.apply(null,x(y.apply(0,arguments))),b)}},before:function(){var a=this.__shady_parentNode;a!==null&&a.__shady_insertBefore(sc.apply(null,x(y.apply(0,arguments))),this)},remove:function(){var a=this.__shady_parentNode;a!==null&&a.__shady_removeChild(this)},replaceWith:function(){var a=y.apply(0,arguments),b=this.__shady_parentNode;if(b!==null){var c=this.__shady_nextSibling;
b.__shady_removeChild(this);b.__shady_insertBefore(sc.apply(null,x(a)),c)}}});var re=window.document;function se(a,b){b==="slot"?(a=a.__shady_parentNode,bc(a)&&F(a).root.v()):a.localName==="slot"&&b==="name"&&(b=ae(a))&&(b.yc(a),b.v())}
var te=J({get previousElementSibling(){var a=F(this);if(a&&a.previousSibling!==void 0){for(a=this.__shady_previousSibling;a&&a.nodeType!==Node.ELEMENT_NODE;)a=a.__shady_previousSibling;return a}return this.__shady_native_previousElementSibling},get nextElementSibling(){var a=F(this);if(a&&a.nextSibling!==void 0){for(a=this.__shady_nextSibling;a&&a.nodeType!==Node.ELEMENT_NODE;)a=a.__shady_nextSibling;return a}return this.__shady_native_nextElementSibling},get slot(){return this.getAttribute("slot")},
set slot(a){this.__shady_setAttribute("slot",a)},get className(){return this.getAttribute("class")||""},set className(a){this.__shady_setAttribute("class",a)},setAttribute:function(a,b){this.ownerDocument!==re?this.__shady_native_setAttribute(a,b):Nd(this,a,b)||(this.__shady_native_setAttribute(a,b),se(this,a))},removeAttribute:function(a){this.ownerDocument!==re?this.__shady_native_removeAttribute(a):Nd(this,a,"")?this.getAttribute(a)===""&&this.__shady_native_removeAttribute(a):(this.__shady_native_removeAttribute(a),
se(this,a))},toggleAttribute:function(a,b){if(this.ownerDocument!==re)return this.__shady_native_toggleAttribute(a,b);if(!Nd(this,a,""))return b=this.__shady_native_toggleAttribute(a,b),se(this,a),b;if(this.getAttribute(a)===""&&!b)return this.__shady_native_toggleAttribute(a,b)}});G.ca||Ed.forEach(function(a){te[a]=Gd(a)});
var xe=J({attachShadow:function(a){if(!this)throw Error("Must provide a host.");if(!a)throw Error("Not enough arguments.");if(a.shadyUpgradeFragment&&!G.zb){var b=a.shadyUpgradeFragment;b.__proto__=ShadowRoot.prototype;b.ab(this,a);ue(b,b);a=b.__noInsertionPoint?null:b.querySelectorAll("slot");b.__noInsertionPoint=void 0;a&&a.length&&(b.Qa(a),b.v());b.host.__shady_native_appendChild(b)}else b=new ve(we,this,a);return this.__CE_shadowRoot=b},get shadowRoot(){var a=F(this);return a&&a.Sc||null}});
qc(te,xe);var ye=document.implementation.createHTMLDocument("inert"),ze=J({get innerHTML(){return ac(this)?Kc(this.localName==="template"?this.content:this,nc):this.__shady_native_innerHTML},set innerHTML(a){if(this.localName==="template")this.__shady_native_innerHTML=a;else{Xd(this);var b=this.localName||"div";b=this.namespaceURI&&this.namespaceURI!==ye.namespaceURI?ye.createElementNS(this.namespaceURI,b):ye.createElement(b);for(G.j?b.__shady_native_innerHTML=a:b.innerHTML=a;a=b.__shady_firstChild;)this.__shady_insertBefore(a)}}});var Ae=J({blur:function(){var a=F(this);(a=(a=a&&a.root)&&a.activeElement)?a.__shady_blur():this.__shady_native_blur()}});G.ca||Fd.forEach(function(a){Ae[a]=Gd(a)});var Be=J({assignedNodes:function(a){if(this.localName==="slot"){var b=this.__shady_getRootNode();b&&H(b)&&b.la();return(b=F(this))?(a&&a.flatten?b.T:b.assignedNodes)||[]:[]}},addEventListener:function(a,b,c){if(this.localName!=="slot"||a==="slotchange")wd.call(this,a,b,c);else{typeof c!=="object"&&(c={capture:!!c});var d=this.__shady_parentNode;if(!d)throw Error("ShadyDOM cannot attach event to slot unless it has a `parentNode`");c.L=this;d.__shady_addEventListener(a,b,c)}},removeEventListener:function(a,
b,c){if(this.localName!=="slot"||a==="slotchange")xd.call(this,a,b,c);else{typeof c!=="object"&&(c={capture:!!c});var d=this.__shady_parentNode;if(!d)throw Error("ShadyDOM cannot attach event to slot unless it has a `parentNode`");c.L=this;d.__shady_removeEventListener(a,b,c)}}});var Ce=J({getElementById:function(a){return a===""?null:ie(this,function(b){return b.id==a},function(b){return!!b})[0]||null}});function De(a,b){for(var c;b&&!a.has(c=b.__shady_getRootNode());)b=c.host;return b}function Ee(a){var b=new Set;for(b.add(a);H(a)&&a.host;)a=a.host.__shady_getRootNode(),b.add(a);return b}
var Fe="__shady_native_"+$b(),Ge=J({get activeElement(){var a=G.j?document.__shady_native_activeElement:document.activeElement;if(!a||!a.nodeType)return null;var b=!!H(this);if(!(this===document||b&&this.host!==a&&this.host.__shady_native_contains(a)))return null;for(b=ae(a);b&&b!==this;)a=b.host,b=ae(a);return this===document?b?null:a:b===this?a:null},elementsFromPoint:function(a,b){a=document[Fe](a,b);if(this===document&&G.useNativeDocumentEFP)return a;a=[].slice.call(a);b=Ee(this);for(var c=new Set,
d=0;d<a.length;d++)c.add(De(b,a[d]));var e=[];c.forEach(function(f){return e.push(f)});return e},elementFromPoint:function(a,b){return this===document&&G.useNativeDocumentEFP?this.__shady_native_elementFromPoint(a,b):this.__shady_elementsFromPoint(a,b)[0]||null}});var He=window.document,Ie=J({importNode:function(a,b){if(a.ownerDocument!==He||a.localName==="template")return this.__shady_native_importNode(a,b);var c=this.__shady_native_importNode(a,!1);if(b)for(a=a.__shady_firstChild;a;a=a.__shady_nextSibling)b=this.__shady_importNode(a,!0),c.__shady_appendChild(b);return c}});var Je=J({dispatchEvent:ud,addEventListener:wd.bind(window),removeEventListener:xd.bind(window)});var Ke={};Object.getOwnPropertyDescriptor(HTMLElement.prototype,"parentElement")&&(Ke.parentElement=de.parentElement);Object.getOwnPropertyDescriptor(HTMLElement.prototype,"contains")&&(Ke.contains=de.contains);Object.getOwnPropertyDescriptor(HTMLElement.prototype,"children")&&(Ke.children=le.children);Object.getOwnPropertyDescriptor(HTMLElement.prototype,"innerHTML")&&(Ke.innerHTML=ze.innerHTML);Object.getOwnPropertyDescriptor(HTMLElement.prototype,"className")&&(Ke.className=te.className);
var N={EventTarget:[Kd],Node:[de,window.EventTarget?null:Kd],Text:[ee],Comment:[ee],CDATASection:[ee],ProcessingInstruction:[ee],Element:[te,le,qe,ee,!G.j||"innerHTML"in Element.prototype?ze:null,window.HTMLSlotElement?null:Be],HTMLElement:[Ae,Ke],HTMLSlotElement:[Be],DocumentFragment:[pe,Ce],Document:[Ie,pe,Ce,Ge],Window:[Je],CharacterData:[qe],XMLHttpRequest:[window.EventTarget?null:Kd]},Le=G.j?null:["innerHTML","textContent"];
function Me(a,b,c,d){b.forEach(function(e){return a&&e&&I(a,e,c,d)})}function Ne(a){var b=a?null:Le,c;for(c in N)Me(window[c]&&window[c].prototype,N[c],a,b)}["Text","Comment","CDATASection","ProcessingInstruction"].forEach(function(a){var b=window[a],c=Object.create(b.prototype);c.__shady_protoIsPatched=!0;Me(c,N.EventTarget);Me(c,N.Node);N[a]&&Me(c,N[a]);b.prototype.__shady_patchedProto=c});
function Oe(a){a.__shady_protoIsPatched=!0;Me(a,N.EventTarget);Me(a,N.Node);Me(a,N.Element);Me(a,N.HTMLElement);Me(a,N.HTMLSlotElement);return a};var Pe=G.La,Qe=G.j;function Re(a,b){if(Pe&&!a.__shady_protoIsPatched&&!H(a)){var c=Object.getPrototypeOf(a),d=c.hasOwnProperty("__shady_patchedProto")&&c.__shady_patchedProto;d||(d=Object.create(c),Oe(d),c.__shady_patchedProto=d);Object.setPrototypeOf(a,d)}Qe||(b===1?$c(a):b===2&&ad(a))}
function Se(a,b,c,d){Re(a,1);d=d||null;var e=E(a),f=d?E(d):null;e.previousSibling=d?f.previousSibling:b.__shady_lastChild;if(f=F(e.previousSibling))f.nextSibling=a;if(f=F(e.nextSibling=d))f.previousSibling=a;e.parentNode=b;d?d===c.firstChild&&(c.firstChild=a):(c.lastChild=a,c.firstChild||(c.firstChild=a));c.childNodes=null}
function be(a,b,c){Re(b,2);var d=E(b);d.firstChild!==void 0&&(d.childNodes=null);if(a.nodeType===Node.DOCUMENT_FRAGMENT_NODE)for(a=a.__shady_native_firstChild;a;a=a.__shady_native_nextSibling)Se(a,b,d,c);else Se(a,b,d,c)}
function ce(a,b){var c=E(a);b=E(b);a===b.firstChild&&(b.firstChild=c.nextSibling);a===b.lastChild&&(b.lastChild=c.previousSibling);a=c.previousSibling;var d=c.nextSibling;a&&(E(a).nextSibling=d);d&&(E(d).previousSibling=a);c.parentNode=c.previousSibling=c.nextSibling=void 0;b.childNodes!==void 0&&(b.childNodes=null)}
function ue(a,b){var c=E(a);if(b||c.firstChild===void 0){c.childNodes=null;var d=c.firstChild=a.__shady_native_firstChild;c.lastChild=a.__shady_native_lastChild;Re(a,2);c=d;for(d=void 0;c;c=c.__shady_native_nextSibling){var e=E(c);e.parentNode=b||a;e.nextSibling=c.__shady_native_nextSibling;e.previousSibling=d||null;d=c;Re(c,1)}}};var Te=J({addEventListener:function(a,b,c){typeof c!=="object"&&(c={capture:!!c});c.L=c.L||this;this.host.__shady_addEventListener(a,b,c)},removeEventListener:function(a,b,c){typeof c!=="object"&&(c={capture:!!c});c.L=c.L||this;this.host.__shady_removeEventListener(a,b,c)}});function Ue(a,b){I(a,Te,b);I(a,Ge,b);I(a,ze,b);I(a,le,b);G.noPatch&&!b?(I(a,de,b),I(a,Ce,b)):G.j||(I(a,Xc),I(a,Vc),I(a,Wc))};var we={},Ve=G.deferConnectionCallbacks&&document.readyState==="loading",We;function Xe(a){var b=[];do b.unshift(a);while(a=a.__shady_parentNode);return b}function ve(a,b,c){if(a!==we)throw new TypeError("Illegal constructor");this.g=null;this.ab(b,c)}n=ve.prototype;
n.ab=function(a,b){this.host=a;this.mode=b&&b.mode;ue(this.host);a=E(this.host);a.root=this;a.Sc=this.mode!=="closed"?this:null;a=E(this);a.firstChild=a.lastChild=a.parentNode=a.nextSibling=a.previousSibling=null;if(G.preferPerformance)for(;a=this.host.__shady_native_firstChild;)this.host.__shady_native_removeChild(a);else this.v()};n.v=function(){var a=this;this.R||(this.R=!0,xc(function(){return a.la()}))};n.Vb=function(){for(var a,b=this;b;)b.R&&(a=b),b=b.Ub();return a};
n.Ub=function(){var a=this.host.__shady_getRootNode();if(H(a)){var b=F(this.host);if(b&&b.ga>0)return a}};n.la=function(){var a=this.R&&this.Vb();a&&a._renderSelf()};n.Qb=function(){!this.Za&&this.R&&this.la()};
n._renderSelf=function(){var a=Ve;Ve=!0;this.R=!1;this.g&&(this.Lb(),this.Jb());if(!G.preferPerformance&&!this.Za)for(var b=this.host.__shady_firstChild;b;b=b.__shady_nextSibling){var c=F(b);b.__shady_native_parentNode!==this.host||b.localName!=="slot"&&c.assignedSlot||this.host.__shady_native_removeChild(b)}this.Za=!0;Ve=a;We&&We()};
n.Lb=function(){this.pa();for(var a=0,b;a<this.g.length;a++)b=this.g[a],this.Ib(b);for(a=this.host.__shady_firstChild;a;a=a.__shady_nextSibling)this.Ta(a);for(a=0;a<this.g.length;a++){b=this.g[a];var c=F(b);if(!c.assignedNodes.length)for(var d=b.__shady_firstChild;d;d=d.__shady_nextSibling)this.Ta(d,b);(d=(d=F(b.__shady_parentNode))&&d.root)&&(d.Ya()||d.R)&&d._renderSelf();this.Pa(c.T,c.assignedNodes);if(d=c.fb){for(var e=0;e<d.length;e++)F(d[e]).Da=null;c.fb=null;d.length>c.assignedNodes.length&&
(c.Ia=!0)}c.Ia&&(c.Ia=!1,this.Va(b))}};n.Ta=function(a,b){var c=E(a),d=c.Da;c.Da=null;b||(b=(b=this.i[a.__shady_slot||"__catchall"])&&b[0]);b?(E(b).assignedNodes.push(a),c.assignedSlot=b):c.assignedSlot=void 0;d!==c.assignedSlot&&c.assignedSlot&&(E(c.assignedSlot).Ia=!0)};n.Ib=function(a){var b=F(a),c=b.assignedNodes;b.assignedNodes=[];b.T=[];if(b.fb=c)for(b=0;b<c.length;b++){var d=F(c[b]);d.Da=d.assignedSlot;d.assignedSlot===a&&(d.assignedSlot=null)}};
n.Pa=function(a,b){for(var c=0,d=void 0;c<b.length&&(d=b[c]);c++)if(d.localName=="slot"){var e=F(d).assignedNodes;e&&e.length&&this.Pa(a,e)}else a.push(b[c])};n.Va=function(a){a.__shady_native_dispatchEvent(new Event("slotchange"));a=F(a);a.assignedSlot&&this.Va(a.assignedSlot)};n.Jb=function(){for(var a=this.g,b=[],c=0;c<a.length;c++){var d=a[c].__shady_parentNode,e=F(d);e&&e.root||!(b.indexOf(d)<0)||b.push(d)}for(a=0;a<b.length;a++)c=b[a],this.xc(c===this?this.host:c,this.Kb(c))};
n.Kb=function(a){var b=[];for(a=a.__shady_firstChild;a;a=a.__shady_nextSibling)if(this.Yb(a))for(var c=F(a).T,d=0;d<c.length;d++)b.push(c[d]);else b.push(a);return b};n.Yb=function(a){return a.localName=="slot"};
n.xc=function(a,b){for(var c=mc(a),d=Id(b,b.length,c,c.length),e=0,f=0,g=void 0;e<d.length&&(g=d[e]);e++){for(var h=0,k=void 0;h<g.da.length&&(k=g.da[h]);h++)k.__shady_native_parentNode===a&&a.__shady_native_removeChild(k),c.splice(g.index+f,1);f-=g.qa}e=0;for(f=void 0;e<d.length&&(f=d[e]);e++)for(g=c[f.index],h=f.index;h<f.index+f.qa;h++)k=b[h],a.__shady_native_insertBefore(k,g),c.splice(h,0,k)};n.Pb=function(){this.I=this.I||[];this.g=this.g||[];this.i=this.i||{}};
n.Qa=function(a){this.Pb();this.I.push.apply(this.I,x(a))};n.pa=function(){this.I&&this.I.length&&(this.cc(this.I),this.I=[])};n.cc=function(a){for(var b,c=0;c<a.length;c++){var d=a[c];ue(d);var e=d.__shady_parentNode;ue(e);e=F(e);e.ga=(e.ga||0)+1;e=this.cb(d);this.i[e]?(b=b||{},b[e]=!0,this.i[e].push(d)):this.i[e]=[d];this.g.push(d)}if(b)for(var f in b)this.i[f]=this.ib(this.i[f])};n.cb=function(a){var b=a.name||a.getAttribute("name")||"__catchall";return a.Db=b};
n.ib=function(a){return a.sort(function(b,c){b=Xe(b);for(var d=Xe(c),e=0;e<b.length;e++){c=b[e];var f=d[e];if(c!==f)return b=nc(c.__shady_parentNode),b.indexOf(c)-b.indexOf(f)}})};n.jc=function(a){if(this.g){this.pa();var b=this.i,c;for(c in b)for(var d=b[c],e=0;e<d.length;e++){var f=d[e];if(kc(a,f)){d.splice(e,1);var g=this.g.indexOf(f);g>=0&&(this.g.splice(g,1),(g=F(f.__shady_parentNode))&&g.ga&&g.ga--);e--;this.kc(f);g=!0}}return g}};
n.yc=function(a){if(this.g){this.pa();var b=a.Db,c=this.cb(a);if(c!==b){b=this.i[b];var d=b.indexOf(a);d>=0&&b.splice(d,1);b=this.i[c]||(this.i[c]=[]);b.push(a);b.length>1&&(this.i[c]=this.ib(b))}}};n.kc=function(a){a=F(a);var b=a.T;if(b)for(var c=0;c<b.length;c++){var d=b[c],e=d.__shady_native_parentNode;e&&e.__shady_native_removeChild(d)}a.T=[];a.assignedNodes=[]};n.Ya=function(){this.pa();return!(!this.g||!this.g.length)};
(function(a){a.__proto__=DocumentFragment.prototype;Ue(a,"__shady_");Ue(a);Object.defineProperties(a,{nodeType:{value:Node.DOCUMENT_FRAGMENT_NODE,configurable:!0},nodeName:{value:"#document-fragment",configurable:!0},nodeValue:{value:null,configurable:!0}});["localName","namespaceURI","prefix"].forEach(function(b){Object.defineProperty(a,b,{value:void 0,configurable:!0})});["ownerDocument","baseURI","isConnected"].forEach(function(b){Object.defineProperty(a,b,{get:function(){return this.host[b]},
configurable:!0})})})(ve.prototype);
if(window.customElements&&window.customElements.define&&G.inUse&&!G.preferPerformance){var Ye=new Map;We=function(){var a=[];Ye.forEach(function(d,e){a.push([e,d])});Ye.clear();for(var b=0;b<a.length;b++){var c=a[b][0];a[b][1]?c.__shadydom_connectedCallback():c.__shadydom_disconnectedCallback()}};Ve&&document.addEventListener("readystatechange",function(){Ve=!1;We()},{once:!0});var Ze=function(a,b,c){var d=0,e="__isConnected"+d++;if(b||c)a.prototype.connectedCallback=a.prototype.__shadydom_connectedCallback=
function(){Ve?Ye.set(this,!0):this[e]||(this[e]=!0,b&&b.call(this))},a.prototype.disconnectedCallback=a.prototype.__shadydom_disconnectedCallback=function(){Ve?this.isConnected||Ye.set(this,!1):this[e]&&(this[e]=!1,c&&c.call(this))};return a},$e=window.customElements.define,af=function(a,b){var c=b.prototype.connectedCallback,d=b.prototype.disconnectedCallback;$e.call(window.customElements,a,Ze(b,c,d));b.prototype.connectedCallback=c;b.prototype.disconnectedCallback=d};window.customElements.define=
af;Object.defineProperty(window.CustomElementRegistry.prototype,"define",{value:af,configurable:!0})}function ae(a){a=a.__shady_getRootNode();if(H(a))return a};function bf(a){this.node=a}n=bf.prototype;n.addEventListener=function(a,b,c){return this.node.__shady_addEventListener(a,b,c)};n.removeEventListener=function(a,b,c){return this.node.__shady_removeEventListener(a,b,c)};n.appendChild=function(a){return this.node.__shady_appendChild(a)};n.insertBefore=function(a,b){return this.node.__shady_insertBefore(a,b)};n.removeChild=function(a){return this.node.__shady_removeChild(a)};n.replaceChild=function(a,b){return this.node.__shady_replaceChild(a,b)};
n.cloneNode=function(a){return this.node.__shady_cloneNode(a)};n.getRootNode=function(a){return this.node.__shady_getRootNode(a)};n.contains=function(a){return this.node.__shady_contains(a)};n.dispatchEvent=function(a){return this.node.__shady_dispatchEvent(a)};n.setAttribute=function(a,b){this.node.__shady_setAttribute(a,b)};n.getAttribute=function(a){return this.node.__shady_native_getAttribute(a)};n.hasAttribute=function(a){return this.node.__shady_native_hasAttribute(a)};n.removeAttribute=function(a){this.node.__shady_removeAttribute(a)};
n.toggleAttribute=function(a,b){return this.node.__shady_toggleAttribute(a,b)};n.attachShadow=function(a){return this.node.__shady_attachShadow(a)};n.focus=function(){this.node.__shady_native_focus()};n.blur=function(){this.node.__shady_blur()};n.importNode=function(a,b){if(this.node.nodeType===Node.DOCUMENT_NODE)return this.node.__shady_importNode(a,b)};n.getElementById=function(a){if(this.node.nodeType===Node.DOCUMENT_NODE)return this.node.__shady_getElementById(a)};
n.elementsFromPoint=function(a,b){return this.node.__shady_elementsFromPoint(a,b)};n.elementFromPoint=function(a,b){return this.node.__shady_elementFromPoint(a,b)};n.querySelector=function(a){return this.node.__shady_querySelector(a)};n.querySelectorAll=function(a,b){return this.node.__shady_querySelectorAll(a,b)};n.assignedNodes=function(a){if(this.node.localName==="slot")return this.node.__shady_assignedNodes(a)};n.append=function(){return this.node.__shady_append.apply(this.node,x(y.apply(0,arguments)))};
n.prepend=function(){return this.node.__shady_prepend.apply(this.node,x(y.apply(0,arguments)))};n.replaceChildren=function(){return this.node.__shady_replaceChildren.apply(this.node,x(y.apply(0,arguments)))};n.after=function(){return this.node.__shady_after.apply(this.node,x(y.apply(0,arguments)))};n.before=function(){return this.node.__shady_before.apply(this.node,x(y.apply(0,arguments)))};n.remove=function(){return this.node.__shady_remove()};
n.replaceWith=function(){return this.node.__shady_replaceWith.apply(this.node,x(y.apply(0,arguments)))};
ca.Object.defineProperties(bf.prototype,{activeElement:{configurable:!0,enumerable:!0,get:function(){if(H(this.node)||this.node.nodeType===Node.DOCUMENT_NODE)return this.node.__shady_activeElement}},_activeElement:{configurable:!0,enumerable:!0,get:function(){return this.activeElement}},host:{configurable:!0,enumerable:!0,get:function(){if(H(this.node))return this.node.host}},parentNode:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_parentNode}},firstChild:{configurable:!0,
enumerable:!0,get:function(){return this.node.__shady_firstChild}},lastChild:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_lastChild}},nextSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_nextSibling}},previousSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_previousSibling}},childNodes:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_childNodes}},parentElement:{configurable:!0,enumerable:!0,
get:function(){return this.node.__shady_parentElement}},firstElementChild:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_firstElementChild}},lastElementChild:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_lastElementChild}},nextElementSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_nextElementSibling}},previousElementSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_previousElementSibling}},
children:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_children}},childElementCount:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_childElementCount}},shadowRoot:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_shadowRoot}},assignedSlot:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_assignedSlot}},isConnected:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_isConnected}},innerHTML:{configurable:!0,
enumerable:!0,get:function(){return this.node.__shady_innerHTML},set:function(a){this.node.__shady_innerHTML=a}},textContent:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_textContent},set:function(a){this.node.__shady_textContent=a}},slot:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_slot},set:function(a){this.node.__shady_slot=a}},className:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_className},set:function(a){this.node.__shady_className=
a}}});function cf(a){Object.defineProperty(bf.prototype,a,{get:function(){return this.node["__shady_"+a]},set:function(b){this.node["__shady_"+a]=b},configurable:!0})}Ed.forEach(function(a){return cf(a)});Fd.forEach(function(a){return cf(a)});var df=new WeakMap;function ef(a){if(H(a)||a instanceof bf)return a;var b=df.get(a);b||(b=new bf(a),df.set(a,b));return b};if(G.inUse){var ff=G.j?function(a){return a}:function(a){ad(a);$c(a);return a};window.ShadyDOM={inUse:G.inUse,patch:ff,isShadyRoot:H,enqueue:xc,flush:yc,flushInitial:function(a){a.Qb()},settings:G,filterMutations:Dc,observeChildren:Bc,unobserveChildren:Cc,deferConnectionCallbacks:G.deferConnectionCallbacks,preferPerformance:G.preferPerformance,handlesDynamicScoping:!0,wrap:G.noPatch?ef:ff,wrapIfNeeded:G.noPatch===!0?ef:function(a){return a},Wrapper:bf,composedPath:jd,noPatch:G.noPatch,patchOnDemand:G.La,
nativeMethods:Mc,nativeTree:Nc,patchElementProto:Oe,querySelectorImplementation:G.querySelectorImplementation};Uc();Ne("__shady_");Object.defineProperty(document,"_activeElement",Ge.activeElement);I(Window.prototype,Je,"__shady_");G.noPatch?G.La&&I(Element.prototype,xe):(Ne(),Dd());yd();window.Event=Ad;window.CustomEvent=Bd;window.MouseEvent=Cd;window.ShadowRoot=ve};var gf=window.Document.prototype.createElement,hf=window.Document.prototype.createElementNS,jf=window.Document.prototype.importNode,kf=window.Document.prototype.prepend,lf=window.Document.prototype.append,mf=window.DocumentFragment.prototype.prepend,nf=window.DocumentFragment.prototype.append,of=window.Node.prototype.cloneNode,pf=window.Node.prototype.appendChild,qf=window.Node.prototype.insertBefore,rf=window.Node.prototype.removeChild,sf=window.Node.prototype.replaceChild,tf=Object.getOwnPropertyDescriptor(window.Node.prototype,
"textContent"),uf=window.Element.prototype.attachShadow,vf=Object.getOwnPropertyDescriptor(window.Element.prototype,"innerHTML"),wf=window.Element.prototype.getAttribute,xf=window.Element.prototype.setAttribute,yf=window.Element.prototype.removeAttribute,zf=window.Element.prototype.toggleAttribute,Af=window.Element.prototype.getAttributeNS,Bf=window.Element.prototype.setAttributeNS,Cf=window.Element.prototype.removeAttributeNS,Df=window.Element.prototype.insertAdjacentElement,Ef=window.Element.prototype.insertAdjacentHTML,
Ff=window.Element.prototype.prepend,Gf=window.Element.prototype.append,Hf=window.Element.prototype.before,If=window.Element.prototype.after,Jf=window.Element.prototype.replaceWith,Kf=window.Element.prototype.remove,Lf=window.HTMLElement,Mf=Object.getOwnPropertyDescriptor(window.HTMLElement.prototype,"innerHTML"),Nf=window.HTMLElement.prototype.insertAdjacentElement,Of=window.HTMLElement.prototype.insertAdjacentHTML;var Pf=function(){var a=new Set;"annotation-xml color-profile font-face font-face-src font-face-uri font-face-format font-face-name missing-glyph".split(" ").forEach(function(b){return a.add(b)});return a}();function Qf(a){var b=Pf.has(a);a=/^[a-z][.0-9_a-z]*-[-.0-9_a-z]*$/.test(a);return!b&&a}var Rf=document.contains?document.contains.bind(document):document.documentElement.contains.bind(document.documentElement);
function O(a){var b=a.isConnected;if(b!==void 0)return b;if(Rf(a))return!0;for(;a&&!(a.__CE_isImportDocument||a instanceof Document);)a=a.parentNode||(window.ShadowRoot&&a instanceof ShadowRoot?a.host:void 0);return!(!a||!(a.__CE_isImportDocument||a instanceof Document))}function Sf(a){var b=a.children;if(b)return Array.prototype.slice.call(b);b=[];for(a=a.firstChild;a;a=a.nextSibling)a.nodeType===Node.ELEMENT_NODE&&b.push(a);return b}
function Tf(a,b){for(;b&&b!==a&&!b.nextSibling;)b=b.parentNode;return b&&b!==a?b.nextSibling:null}
function Uf(a,b,c){for(var d=a;d;){if(d.nodeType===Node.ELEMENT_NODE){var e=d;b(e);var f=e.localName;if(f==="link"&&e.getAttribute("rel")==="import"){d=e.import;c===void 0&&(c=new Set);if(d instanceof Node&&!c.has(d))for(c.add(d),d=d.firstChild;d;d=d.nextSibling)Uf(d,b,c);d=Tf(a,e);continue}else if(f==="template"){d=Tf(a,e);continue}if(e=e.__CE_shadowRoot)for(e=e.firstChild;e;e=e.nextSibling)Uf(e,b,c)}d=d.firstChild?d.firstChild:Tf(a,d)}};function Vf(){var a=!(Wf==null||!Wf.noDocumentConstructionObserver),b=!(Wf==null||!Wf.shadyDomFastWalk);this.X=[];this.Ca=[];this.O=!1;this.shadyDomFastWalk=b;this.bd=!a}function Xf(a,b,c,d){var e=window.ShadyDOM;if(a.shadyDomFastWalk&&e&&e.inUse){if(b.nodeType===Node.ELEMENT_NODE&&c(b),b.querySelectorAll)for(a=e.nativeMethods.querySelectorAll.call(b,"*"),b=0;b<a.length;b++)c(a[b])}else Uf(b,c,d)}function Yf(a,b){a.O=!0;a.X.push(b)}function Zf(a,b){a.O=!0;a.Ca.push(b)}
function $f(a,b){a.O&&Xf(a,b,function(c){return ag(a,c)})}function ag(a,b){if(a.O&&!b.__CE_patched){b.__CE_patched=!0;for(var c=0;c<a.X.length;c++)a.X[c](b);for(c=0;c<a.Ca.length;c++)a.Ca[c](b)}}function P(a,b){var c=[];Xf(a,b,function(e){return c.push(e)});for(b=0;b<c.length;b++){var d=c[b];d.__CE_state===1?a.connectedCallback(d):bg(a,d)}}function R(a,b){var c=[];Xf(a,b,function(e){return c.push(e)});for(b=0;b<c.length;b++){var d=c[b];d.__CE_state===1&&a.disconnectedCallback(d)}}
function cg(a,b,c){c=c===void 0?{}:c;var d=c.cd,e=c.upgrade||function(g){return bg(a,g)},f=[];Xf(a,b,function(g){a.O&&ag(a,g);if(g.localName==="link"&&g.getAttribute("rel")==="import"){var h=g.import;h instanceof Node&&(h.__CE_isImportDocument=!0,h.__CE_registry=document.__CE_registry);h&&h.readyState==="complete"?h.__CE_documentLoadHandled=!0:g.addEventListener("load",function(){var k=g.import;if(!k.__CE_documentLoadHandled){k.__CE_documentLoadHandled=!0;var l=new Set;d&&(d.forEach(function(p){return l.add(p)}),
l.delete(k));cg(a,k,{cd:l,upgrade:e})}})}else f.push(g)},d);for(b=0;b<f.length;b++)e(f[b])}function bg(a,b){try{var c=a.bc(b.ownerDocument,b.localName);c&&a.zc(b,c)}catch(d){dg(d)}}n=Vf.prototype;
n.zc=function(a,b){if(a.__CE_state===void 0){b.constructionStack.push(a);try{try{if(new b.constructorFunction!==a)throw Error("The custom element constructor did not produce the element being upgraded.");}finally{b.constructionStack.pop()}}catch(f){throw a.__CE_state=2,f;}a.__CE_state=1;a.__CE_definition=b;if(b.attributeChangedCallback&&a.hasAttributes()){b=b.observedAttributes;for(var c=0;c<b.length;c++){var d=b[c],e=a.getAttribute(d);e!==null&&this.attributeChangedCallback(a,d,null,e,null)}}O(a)&&
this.connectedCallback(a)}};n.connectedCallback=function(a){var b=a.__CE_definition;if(b.connectedCallback)try{b.connectedCallback.call(a)}catch(c){dg(c)}};n.disconnectedCallback=function(a){var b=a.__CE_definition;if(b.disconnectedCallback)try{b.disconnectedCallback.call(a)}catch(c){dg(c)}};n.attributeChangedCallback=function(a,b,c,d,e){var f=a.__CE_definition;if(f.attributeChangedCallback&&f.observedAttributes.indexOf(b)>-1)try{f.attributeChangedCallback.call(a,b,c,d,e)}catch(g){dg(g)}};
n.bc=function(a,b){var c=a.__CE_registry;if(c&&(a.defaultView||a.__CE_isImportDocument))return eg(c,b)};
function fg(a,b,c,d){var e=b.__CE_registry;if(e&&(d===null||d==="http://www.w3.org/1999/xhtml")&&(e=eg(e,c)))try{var f=new e.constructorFunction;if(f.__CE_state===void 0||f.__CE_definition===void 0)throw Error("Failed to construct '"+c+"': The returned value was not constructed with the HTMLElement constructor.");if(f.namespaceURI!=="http://www.w3.org/1999/xhtml")throw Error("Failed to construct '"+c+"': The constructed element's namespace must be the HTML namespace.");if(f.hasAttributes())throw Error("Failed to construct '"+
c+"': The constructed element must not have any attributes.");if(f.firstChild!==null)throw Error("Failed to construct '"+c+"': The constructed element must not have any children.");if(f.parentNode!==null)throw Error("Failed to construct '"+c+"': The constructed element must not have a parent node.");if(f.ownerDocument!==b)throw Error("Failed to construct '"+c+"': The constructed element's owner document is incorrect.");if(f.localName!==c)throw Error("Failed to construct '"+c+"': The constructed element's local name is incorrect.");
return f}catch(g){return dg(g),b=d===null?gf.call(b,c):hf.call(b,d,c),Object.setPrototypeOf(b,HTMLUnknownElement.prototype),b.__CE_state=2,b.__CE_definition=void 0,ag(a,b),b}b=d===null?gf.call(b,c):hf.call(b,d,c);ag(a,b);return b}
function dg(a){var b="",c="",d=0,e=0;a instanceof Error?(b=a.message,c=a.sourceURL||a.fileName||"",d=a.line||a.lineNumber||0,e=a.column||a.columnNumber||0):b="Uncaught "+String(a);var f=void 0;ErrorEvent.prototype.initErrorEvent===void 0?f=new ErrorEvent("error",{cancelable:!0,message:b,filename:c,lineno:d,colno:e,error:a}):(f=document.createEvent("ErrorEvent"),f.initErrorEvent("error",!1,!0,b,c,d),f.preventDefault=function(){Object.defineProperty(this,"defaultPrevented",{configurable:!0,get:function(){return!0}})});
f.error===void 0&&Object.defineProperty(f,"error",{configurable:!0,enumerable:!0,get:function(){return a}});window.dispatchEvent(f);f.defaultPrevented||console.error(a)};function gg(){var a=this;this.u=void 0;this.gb=new Promise(function(b){a.nc=b})}gg.prototype.resolve=function(a){if(this.u)throw Error("Already resolved.");this.u=a;this.nc(a)};function hg(a){var b=document;this.P=void 0;this.H=a;this.ha=b;cg(this.H,this.ha);this.ha.readyState==="loading"&&(this.P=new MutationObserver(this.Xb.bind(this)),this.P.observe(this.ha,{childList:!0,subtree:!0}))}hg.prototype.disconnect=function(){this.P&&this.P.disconnect()};hg.prototype.Xb=function(a){var b=this.ha.readyState;b!=="interactive"&&b!=="complete"||this.disconnect();for(b=0;b<a.length;b++)for(var c=a[b].addedNodes,d=0;d<c.length;d++)cg(this.H,c[d])};function S(a){this.ja=new Map;this.ka=new Map;this.Sa=new Map;this.za=!1;this.Ha=new Map;this.ia=function(b){return b()};this.N=!1;this.oa=[];this.H=a;this.Ua=a.bd?new hg(a):void 0}n=S.prototype;n.Rc=function(a,b){var c=this;if(!(b instanceof Function))throw new TypeError("Custom element constructor getters must be functions.");ig(this,a);this.ja.set(a,b);this.oa.push(a);this.N||(this.N=!0,this.ia(function(){return c.Wa()}))};
n.define=function(a,b){var c=this;if(!(b instanceof Function))throw new TypeError("Custom element constructors must be functions.");ig(this,a);jg(this,a,b);this.oa.push(a);this.N||(this.N=!0,this.ia(function(){return c.Wa()}))};
function ig(a,b){if(!Qf(b))throw new SyntaxError("The element name '"+b+"' is not valid.");if(eg(a,b)&&!window.enableHotReplacement)throw Error("A custom element with name '"+(b+"' has already been defined."));if(a.za)throw Error("A custom element is already being defined.");}
function jg(a,b,c){a.za=!0;var d;try{var e=c.prototype;if(!(e instanceof Object))throw new TypeError("The custom element constructor's prototype is not an object.");var f=function(p){var v=e[p];if(v!==void 0&&!(v instanceof Function))throw Error("The '"+p+"' callback must be a function.");return v};var g=f("connectedCallback");var h=f("disconnectedCallback");var k=f("adoptedCallback");var l=(d=f("attributeChangedCallback"))&&c.observedAttributes||[]}catch(p){throw p;}finally{a.za=!1}c={localName:b,
constructorFunction:c,connectedCallback:g,disconnectedCallback:h,adoptedCallback:k,attributeChangedCallback:d,observedAttributes:l,constructionStack:[]};a.ka.set(b,c);a.Sa.set(c.constructorFunction,c);return c}n.upgrade=function(a){cg(this.H,a)};
n.Wa=function(){var a=this;if(this.N!==!1){this.N=!1;for(var b=[],c=this.oa,d=new Map,e=0;e<c.length;e++)d.set(c[e],[]);cg(this.H,document,{upgrade:function(k){if(k.__CE_state===void 0){var l=k.localName,p=d.get(l);p?p.push(k):a.ka.has(l)&&b.push(k)}}});for(e=0;e<b.length;e++)bg(this.H,b[e]);for(e=0;e<c.length;e++){for(var f=c[e],g=d.get(f),h=0;h<g.length;h++)bg(this.H,g[h]);(f=this.Ha.get(f))&&f.resolve(void 0)}c.length=0}};n.get=function(a){if(a=eg(this,a))return a.constructorFunction};
n.whenDefined=function(a){if(!Qf(a))return Promise.reject(new SyntaxError("'"+a+"' is not a valid custom element name."));var b=this.Ha.get(a);if(b)return b.gb;b=new gg;this.Ha.set(a,b);var c=this.ka.has(a)||this.ja.has(a);a=this.oa.indexOf(a)===-1;c&&a&&b.resolve(void 0);return b.gb};n.polyfillWrapFlushCallback=function(a){this.Ua&&this.Ua.disconnect();var b=this.ia;this.ia=function(c){return a(function(){return b(c)})}};
function eg(a,b){var c=a.ka.get(b);if(c)return c;if(c=a.ja.get(b)){a.ja.delete(b);try{return jg(a,b,c())}catch(d){dg(d)}}}S.prototype.define=S.prototype.define;S.prototype.upgrade=S.prototype.upgrade;S.prototype.get=S.prototype.get;S.prototype.whenDefined=S.prototype.whenDefined;S.prototype.polyfillDefineLazy=S.prototype.Rc;S.prototype.polyfillWrapFlushCallback=S.prototype.polyfillWrapFlushCallback;function kg(a,b,c){function d(e){return function(){for(var f=y.apply(0,arguments),g=[],h=[],k=0;k<f.length;k++){var l=f[k];l instanceof Element&&O(l)&&h.push(l);if(l instanceof DocumentFragment)for(l=l.firstChild;l;l=l.nextSibling)g.push(l);else g.push(l)}e.apply(this,f);for(f=0;f<h.length;f++)R(a,h[f]);if(O(this))for(h=0;h<g.length;h++)f=g[h],f instanceof Element&&P(a,f)}}c.prepend!==void 0&&(b.prepend=d(c.prepend));c.append!==void 0&&(b.append=d(c.append))};function lg(a){Document.prototype.createElement=function(b){return fg(a,this,b,null)};Document.prototype.importNode=function(b,c){b=jf.call(this,b,!!c);this.__CE_registry?cg(a,b):$f(a,b);return b};Document.prototype.createElementNS=function(b,c){return fg(a,this,c,b)};kg(a,Document.prototype,{prepend:kf,append:lf})};function mg(a){function b(d){return function(){for(var e=y.apply(0,arguments),f=[],g=[],h=0;h<e.length;h++){var k=e[h];k instanceof Element&&O(k)&&g.push(k);if(k instanceof DocumentFragment)for(k=k.firstChild;k;k=k.nextSibling)f.push(k);else f.push(k)}d.apply(this,e);for(e=0;e<g.length;e++)R(a,g[e]);if(O(this))for(g=0;g<f.length;g++)e=f[g],e instanceof Element&&P(a,e)}}var c=Element.prototype;Hf!==void 0&&(c.before=b(Hf));If!==void 0&&(c.after=b(If));Jf!==void 0&&(c.replaceWith=function(){for(var d=
y.apply(0,arguments),e=[],f=[],g=0;g<d.length;g++){var h=d[g];h instanceof Element&&O(h)&&f.push(h);if(h instanceof DocumentFragment)for(h=h.firstChild;h;h=h.nextSibling)e.push(h);else e.push(h)}g=O(this);Jf.apply(this,d);for(d=0;d<f.length;d++)R(a,f[d]);if(g)for(R(a,this),f=0;f<e.length;f++)d=e[f],d instanceof Element&&P(a,d)});Kf!==void 0&&(c.remove=function(){var d=O(this);Kf.call(this);d&&R(a,this)})};function ng(a){function b(e,f){Object.defineProperty(e,"innerHTML",{enumerable:f.enumerable,configurable:!0,get:f.get,set:function(g){var h=this,k=void 0;O(this)&&(k=[],Xf(a,this,function(v){v!==h&&k.push(v)}));f.set.call(this,g);if(k)for(var l=0;l<k.length;l++){var p=k[l];p.__CE_state===1&&a.disconnectedCallback(p)}this.ownerDocument.__CE_registry?cg(a,this):$f(a,this);return g}})}function c(e,f){e.insertAdjacentElement=function(g,h){var k=O(h);g=f.call(this,g,h);k&&R(a,h);O(g)&&P(a,h);return g}}
function d(e,f){function g(h,k){for(var l=[];h!==k;h=h.nextSibling)l.push(h);for(k=0;k<l.length;k++)cg(a,l[k])}e.insertAdjacentHTML=function(h,k){h=h.toLowerCase();if(h==="beforebegin"){var l=this.previousSibling;f.call(this,h,k);g(l||this.parentNode.firstChild,this)}else if(h==="afterbegin")l=this.firstChild,f.call(this,h,k),g(this.firstChild,l);else if(h==="beforeend")l=this.lastChild,f.call(this,h,k),g(l||this.firstChild,null);else if(h==="afterend")l=this.nextSibling,f.call(this,h,k),g(this.nextSibling,
l);else throw new SyntaxError("The value provided ("+String(h)+") is not one of 'beforebegin', 'afterbegin', 'beforeend', or 'afterend'.");}}uf&&(Element.prototype.attachShadow=function(e){e=uf.call(this,e);if(a.O&&!e.__CE_patched){e.__CE_patched=!0;for(var f=0;f<a.X.length;f++)a.X[f](e)}return this.__CE_shadowRoot=e});vf&&vf.get?b(Element.prototype,vf):Mf&&Mf.get?b(HTMLElement.prototype,Mf):Zf(a,function(e){b(e,{enumerable:!0,configurable:!0,get:function(){return of.call(this,!0).innerHTML},set:function(f){var g=
this.localName==="template",h=g?this.content:this,k=hf.call(document,this.namespaceURI,this.localName);for(k.innerHTML=f;h.childNodes.length>0;)rf.call(h,h.childNodes[0]);for(f=g?k.content:k;f.childNodes.length>0;)pf.call(h,f.childNodes[0])}})});Element.prototype.setAttribute=function(e,f){if(this.__CE_state!==1)return xf.call(this,e,f);var g=wf.call(this,e);xf.call(this,e,f);f=wf.call(this,e);a.attributeChangedCallback(this,e,g,f,null)};Element.prototype.setAttributeNS=function(e,f,g){if(this.__CE_state!==
1)return Bf.call(this,e,f,g);var h=Af.call(this,e,f);Bf.call(this,e,f,g);g=Af.call(this,e,f);a.attributeChangedCallback(this,f,h,g,e)};Element.prototype.removeAttribute=function(e){if(this.__CE_state!==1)return yf.call(this,e);var f=wf.call(this,e);yf.call(this,e);f!==null&&a.attributeChangedCallback(this,e,f,null,null)};zf&&(Element.prototype.toggleAttribute=function(e,f){if(this.__CE_state!==1)return zf.call(this,e,f);var g=wf.call(this,e),h=g!==null;f=zf.call(this,e,f);if(h!==f){var k;a==null||
(k=a.attributeChangedCallback)==null||k.call(a,this,e,g,f?"":null,null)}return f});Element.prototype.removeAttributeNS=function(e,f){if(this.__CE_state!==1)return Cf.call(this,e,f);var g=Af.call(this,e,f);Cf.call(this,e,f);var h=Af.call(this,e,f);g!==h&&a.attributeChangedCallback(this,f,g,h,e)};Nf?c(HTMLElement.prototype,Nf):Df&&c(Element.prototype,Df);Of?d(HTMLElement.prototype,Of):Ef&&d(Element.prototype,Ef);kg(a,Element.prototype,{prepend:Ff,append:Gf});mg(a)};var og={};function pg(a){function b(){var c=this.constructor;var d=document.__CE_registry.Sa.get(c);if(!d)throw Error("Failed to construct a custom element: The constructor was not registered with `customElements`.");var e=d.constructionStack;if(e.length===0)return e=gf.call(document,d.localName),Object.setPrototypeOf(e,c.prototype),e.__CE_state=1,e.__CE_definition=d,ag(a,e),e;var f=e.length-1,g=e[f];if(g===og)throw Error("Failed to construct '"+d.localName+"': This element was already constructed.");e[f]=
og;Object.setPrototypeOf(g,c.prototype);ag(a,g);return g}b.prototype=Lf.prototype;Object.defineProperty(HTMLElement.prototype,"constructor",{writable:!0,configurable:!0,enumerable:!1,value:b});window.HTMLElement=b};function qg(a){function b(c,d){Object.defineProperty(c,"textContent",{enumerable:d.enumerable,configurable:!0,get:d.get,set:function(e){if(this.nodeType===Node.TEXT_NODE)d.set.call(this,e);else{var f=void 0;if(this.firstChild){var g=this.childNodes,h=g.length;if(h>0&&O(this)){f=Array(h);for(var k=0;k<h;k++)f[k]=g[k]}}d.set.call(this,e);if(f)for(e=0;e<f.length;e++)R(a,f[e])}}})}Node.prototype.insertBefore=function(c,d){if(c instanceof DocumentFragment){var e=Sf(c);c=qf.call(this,c,d);if(O(this))for(d=
0;d<e.length;d++)P(a,e[d]);return c}e=c instanceof Element&&O(c);d=qf.call(this,c,d);e&&R(a,c);O(this)&&P(a,c);return d};Node.prototype.appendChild=function(c){if(c instanceof DocumentFragment){var d=Sf(c);c=pf.call(this,c);if(O(this))for(var e=0;e<d.length;e++)P(a,d[e]);return c}d=c instanceof Element&&O(c);e=pf.call(this,c);d&&R(a,c);O(this)&&P(a,c);return e};Node.prototype.cloneNode=function(c){c=of.call(this,!!c);this.ownerDocument.__CE_registry?cg(a,c):$f(a,c);return c};Node.prototype.removeChild=
function(c){var d=c instanceof Element&&O(c),e=rf.call(this,c);d&&R(a,c);return e};Node.prototype.replaceChild=function(c,d){if(c instanceof DocumentFragment){var e=Sf(c);c=sf.call(this,c,d);if(O(this))for(R(a,d),d=0;d<e.length;d++)P(a,e[d]);return c}e=c instanceof Element&&O(c);var f=sf.call(this,c,d),g=O(this);g&&R(a,d);e&&R(a,c);g&&P(a,c);return f};tf&&tf.get?b(Node.prototype,tf):Yf(a,function(c){b(c,{enumerable:!0,configurable:!0,get:function(){for(var d=[],e=this.firstChild;e;e=e.nextSibling)e.nodeType!==
Node.COMMENT_NODE&&d.push(e.textContent);return d.join("")},set:function(d){for(;this.firstChild;)rf.call(this,this.firstChild);d!=null&&d!==""&&pf.call(this,document.createTextNode(d))}})})};var Wf=window.customElements;function rg(){var a=new Vf;pg(a);lg(a);kg(a,DocumentFragment.prototype,{prepend:mf,append:nf});qg(a);ng(a);window.CustomElementRegistry=S;a=new S(a);document.__CE_registry=a;Object.defineProperty(window,"customElements",{configurable:!0,enumerable:!0,value:a})}Wf&&!Wf.forcePolyfill&&typeof Wf.define=="function"&&typeof Wf.get=="function"||rg();window.__CE_installPolyfill=rg;/*

Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
Code distributed by Google as part of the polymer project is also
subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
*/
function sg(){this.end=this.start=0;this.rules=this.parent=this.previous=null;this.cssText=this.parsedCssText="";this.atRule=!1;this.type=0;this.parsedSelector=this.selector=this.keyframesName=""}
function tg(a){var b=a=a.replace(ug,"").replace(vg,""),c=new sg;c.start=0;c.end=b.length;for(var d=c,e=0,f=b.length;e<f;e++)if(b[e]==="{"){d.rules||(d.rules=[]);var g=d,h=g.rules[g.rules.length-1]||null;d=new sg;d.start=e+1;d.parent=g;d.previous=h;g.rules.push(d)}else b[e]==="}"&&(d.end=e+1,d=d.parent||c);return wg(c,a)}
function wg(a,b){var c=b.substring(a.start,a.end-1);a.parsedCssText=a.cssText=c.trim();a.parent&&(c=b.substring(a.previous?a.previous.end:a.parent.start,a.start-1),c=_expandUnicodeEscapes$$module$third_party$javascript$polymer$v2$shadycss$src$css_parse(c),c=c.replace(xg," "),c=c.substring(c.lastIndexOf(";")+1),c=a.parsedSelector=a.selector=c.trim(),a.atRule=c.indexOf("@")===0,a.atRule?c.indexOf("@media")===0?a.type=4:c.match(yg)&&(a.type=7,a.keyframesName=a.selector.split(xg).pop()):a.type=c.indexOf("--")===
0?1E3:1);if(c=a.rules)for(var d=0,e=c.length,f=void 0;d<e&&(f=c[d]);d++)wg(f,b);return a}function _expandUnicodeEscapes$$module$third_party$javascript$polymer$v2$shadycss$src$css_parse(a){return a.replace(/\\([0-9a-f]{1,6})\s/gi,function(){for(var b=arguments[1],c=6-b.length;c--;)b="0"+b;return"\\"+b})}
function zg(a,b,c){c=c===void 0?"":c;var d="";if(a.cssText||a.rules){var e=a.rules;if(e&&!_hasMixinRules$$module$third_party$javascript$polymer$v2$shadycss$src$css_parse(e))for(var f=0,g=e.length,h=void 0;f<g&&(h=e[f]);f++)d=zg(h,b,d);else b?b=a.cssText:(b=a.cssText,b=b.replace(Ag,"").replace(Bg,""),b=b.replace(Cg,"").replace(Dg,"")),(d=b.trim())&&(d="  "+d+"\n")}d&&(a.selector&&(c+=a.selector+" {\n"),c+=d,a.selector&&(c+="}\n\n"));return c}
function _hasMixinRules$$module$third_party$javascript$polymer$v2$shadycss$src$css_parse(a){a=a[0];return!!a&&!!a.selector&&a.selector.indexOf("--")===0}var ug=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//gim,vg=/@import[^;]*;/gim,Ag=/(?:^[^;\-\s}]+)?--[^;{}]*?:[^{};]*?(?:[;\n]|$)/gim,Bg=/(?:^[^;\-\s}]+)?--[^;{}]*?:[^{};]*?{[^}]*?}(?:[;\n]|$)?/gim,Cg=/@apply\s*\(?[^);]*\)?\s*(?:[;\n]|$)?/gim,Dg=/[^;:]*?:[^;]*?var\([^;]*\)(?:[;\n]|$)?/gim,yg=/^@[^\s]*keyframes/,xg=/\s+/g;var T=!(window.ShadyDOM&&window.ShadyDOM.inUse),Eg;function Fg(a){Eg=a&&a.shimcssproperties?!1:T||!(navigator.userAgent.match(/AppleWebKit\/601|Edge\/15/)||!window.CSS||!CSS.supports||!CSS.supports("box-shadow","0 0 0 var(--foo)"))}var Gg;window.ShadyCSS&&window.ShadyCSS.cssBuild!==void 0&&(Gg=window.ShadyCSS.cssBuild);var Lg=!(!window.ShadyCSS||!window.ShadyCSS.disableRuntime);
window.ShadyCSS&&window.ShadyCSS.nativeCss!==void 0?Eg=window.ShadyCSS.nativeCss:window.ShadyCSS?(Fg(window.ShadyCSS),window.ShadyCSS=void 0):Fg(window.WebComponents&&window.WebComponents.flags);var U=Eg;var Mg=/(?:^|[;\s{]\s*)(--[\w-]*?)\s*:\s*(?:((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};{])+)|\{([^}]*)\}(?:(?=[;\s}])|$))/gi,Ng=/(?:^|\W+)@apply\s*\(?([^);\n]*)\)?/gi,Og=/(--[\w-]+)\s*([:,;)]|$)/gi,Pg=/(animation\s*:)|(animation-name\s*:)/,Qg=/@media\s(.*)/,Rg=/\{[^}]*\}/g;var Sg=new Set;function Tg(a,b){if(!a)return"";typeof a==="string"&&(a=tg(a));b&&Ug(a,b);return zg(a,U)}function Vg(a){!a.__cssRules&&a.textContent&&(a.__cssRules=tg(a.textContent));return a.__cssRules||null}function Wg(a){return!!a.parent&&a.parent.type===7}function Ug(a,b,c,d){if(a){var e=!1,f=a.type;if(d&&f===4){var g=a.selector.match(Qg);g&&(window.matchMedia(g[1]).matches||(e=!0))}f===1?b(a):c&&f===7?c(a):f===1E3&&(e=!0);if((a=a.rules)&&!e)for(e=0,f=a.length,g=void 0;e<f&&(g=a[e]);e++)Ug(g,b,c,d)}}
function Xg(a,b,c,d){var e=document.createElement("style");b&&e.setAttribute("scope",b);e.textContent=a;if(window.enableHotReplacement&&(a=document.head.querySelector("style[scope="+b+"]")))return a.parentElement.replaceChild(e,a),e;Yg(e,c,d);return e}var Zg=null;function $g(a){a=document.createComment(" Shady DOM styles for "+a+" ");var b=document.head;b.insertBefore(a,(Zg?Zg.nextSibling:null)||b.firstChild);return Zg=a}
function Yg(a,b,c){b=b||document.head;b.insertBefore(a,c&&c.nextSibling||b.firstChild);Zg?a.compareDocumentPosition(Zg)===Node.DOCUMENT_POSITION_PRECEDING&&(Zg=a):Zg=a}function ah(a,b){for(var c=0,d=a.length;b<d;b++)if(a[b]==="(")c++;else if(a[b]===")"&&--c===0)return b;return-1}
function bh(a,b){var c=a.indexOf("var(");if(c===-1)return b(a,"","","");var d=ah(a,c+3),e=a.substring(c+4,d);c=a.substring(0,c);a=bh(a.substring(d+1),b);d=e.indexOf(",");return d===-1?b(c,e.trim(),"",a):b(c,e.substring(0,d).trim(),e.substring(d+1).trim(),a)}function ch(a,b){T?a.setAttribute("class",b):window.ShadyDOM.nativeMethods.setAttribute.call(a,"class",b)}var dh=window.ShadyDOM&&window.ShadyDOM.wrap||function(a){return a};
function eh(a){var b=a.localName,c="";b?b.indexOf("-")>-1||(c=b,b=a.getAttribute&&a.getAttribute("is")||""):(b=a.is,c=a.extends);return{is:b,ea:c}}function fh(a){for(var b=[],c="",d=0;d>=0&&d<a.length;d++)if(a[d]==="("){var e=ah(a,d);c+=a.slice(d,e+1);d=e}else a[d]===","?(b.push(c),c=""):c+=a[d];c&&b.push(c);return b}
function gh(a){if(Gg!==void 0)return Gg;if(a.__cssBuild===void 0){var b=a.getAttribute("css-build");if(b)a.__cssBuild=b;else{a:{b=a.localName==="template"?a.content.firstChild:a.firstChild;if(b instanceof Comment&&(b=b.textContent.trim().split(":"),b[0]==="css-build")){b=b[1];break a}b=""}if(b!==""){var c=a.localName==="template"?a.content.firstChild:a.firstChild;c.parentNode.removeChild(c)}a.__cssBuild=b}}return a.__cssBuild||""}
function hh(a){a=a===void 0?"":a;return a!==""&&U?T?a==="shadow":a==="shady":!1};function ih(a,b){var c=window.shadyCSSStyleTransformHooks;return c&&(c=c.didTransformSelector,typeof c==="function")?c(a,b):a}function jh(){}function kh(a,b){var c=V;c.na(a,function(d){c.element(d,b||"")})}n=jh.prototype;n.na=function(a,b){a.nodeType===Node.ELEMENT_NODE&&b(a);if(a=a.localName==="template"?(a.content||a._content||a).childNodes:a.children||a.childNodes)for(var c=0;c<a.length;c++)this.na(a[c],b)};
n.element=function(a,b,c){if(b)if(a.classList)c?(a.classList.remove("style-scope"),a.classList.remove(b)):(a.classList.add("style-scope"),a.classList.add(b));else if(a.getAttribute){var d=a.getAttribute("class");c?d&&(b=d.replace("style-scope","").replace(b,""),ch(a,b)):ch(a,(d?d+" ":"")+"style-scope "+b)}};function lh(a,b,c){var d=V;d.na(a,function(e){d.element(e,b,!0);d.element(e,c)})}function mh(a,b){var c=V;c.na(a,function(d){c.element(d,b||"",!0)})}
function nh(a,b,c,d,e){var f=V;e=e===void 0?"":e;e===""&&(T||(d===void 0?"":d)==="shady"?e=Tg(b,c):(a=eh(a),e=oh(f,b,a.is,a.ea,c)+"\n\n"));return e.trim()}function oh(a,b,c,d,e){var f=a.ya(c,d);c=a.Ra(c);return Tg(b,function(g){g.Mc||(a.jb(g,a.Fa,c,f),g.Mc=!0);e&&e(g,c,f)})}n.Ra=function(a){return a?"."+a:""};n.ya=function(a,b){return b?"[is="+a+"]":a};n.jb=function(a,b,c,d){a.selector=a.o=this.kb(a,b,c,d)};
n.kb=function(a,b,c,d){var e=fh(a.selector);if(!Wg(a)){a=0;for(var f=e.length,g=void 0;a<f&&(g=e[a]);a++)e[a]=b.call(this,g,c,d)}return e.filter(function(h){return!!h}).join(",")};n.mb=function(a){return a.replace(ph,function(b,c,d){d.indexOf("+")>-1?d=d.replace(/\+/g,"___"):d.indexOf("___")>-1&&(d=d.replace(/___/g,"+"));return":"+c+"("+d+")"})};
n.hc=function(a){for(var b=[],c;c=a.match(qh);){var d=c.index,e=ah(a,d);if(e===-1)throw Error(c.input+" selector missing ')'");c=a.slice(d,e+1);a=a.replace(c,"\ue000");b.push(c)}return{Ma:a,matches:b}};n.mc=function(a,b){var c=a.split("\ue000");return b.reduce(function(d,e,f){return d+e+c[f+1]},c[0])};
n.Fa=function(a,b,c){var d=this,e=!1;a=a.trim();var f=ph.test(a);f&&(a=a.replace(ph,function(k,l,p){return":"+l+"("+p.replace(/\s/g,"")+")"}),a=this.mb(a));var g=qh.test(a);if(g){var h=this.hc(a);a=h.Ma;h=h.matches}a=a.replace(rh,":host $1");a=a.replace(sh,function(k,l,p){e||(k=d.uc(p,l,b,c),e=e||k.stop,l=k.Cc,p=k.value);return l+p});g&&(a=this.mc(a,h));f&&(a=this.mb(a));a=a.replace(th,function(k,l,p,v){return'[dir="'+p+'"] '+l+v+", "+l+'[dir="'+p+'"]'+v});return ih(a,b)};
n.uc=function(a,b,c,d){var e=a.indexOf("::slotted");a.indexOf(":host")>=0?a=this.wc(a,d):e!==0&&(a=c?this.lb(a,c):a);c=!1;e>=0&&(b="",c=!0);if(c){var f=!0;c&&(a=a.replace(uh,function(g,h){return" > "+h}))}return{value:a,Cc:b,stop:f}};n.lb=function(a,b){a=a.split(/(\[.+?\])/);for(var c=[],d=0;d<a.length;d++)if(d%2===1)c.push(a[d]);else{var e=a[d];if(e!==""||d!==a.length-1)e=e.split(":"),e[0]+=b,c.push(e.join(":"))}return c.join("")};
n.wc=function(a,b){var c=a.match(vh);return(c=c&&c[2].trim()||"")?c[0].match(wh)?a.replace(vh,function(d,e,f){return b+f}):c.split(wh)[0]===b?c:"should_not_match":a.replace(":host",b)};function xh(a){a.selector===":root"&&(a.selector="html")}n.vc=function(a){return a.match(":host")?"":a.match("::slotted")?this.Fa(a,":not(.style-scope)"):ih(this.lb(a.trim(),":not(.style-scope)"),":not(.style-scope)")};ca.Object.defineProperties(jh.prototype,{fa:{configurable:!0,enumerable:!0,get:function(){return"style-scope"}}});
var ph=/:(nth[-\w]+)\(([^)]+)\)/,sh=/(^|[\s>+~]+)((?:\[.+?\]|[^\s>+~=[])+)/g,wh=/[[.:#*]/,rh=RegExp("^(::slotted)"),vh=/(:host)(?:\(((?:\([^)(]*\)|[^)(]*)+?)\))/,uh=/(?:::slotted)(?:\(((?:\([^)(]*\)|[^)(]*)+?)\))/,th=/(.*):dir\((?:(ltr|rtl))\)(.*)/,qh=/:(?:matches|any|-(?:webkit|moz)-any)/,V=new jh;function yh(a,b,c,d,e){this.B=a||null;this.placeholder=b||null;this.Ka=c||[];this.U=null;this.cssBuild=e||"";this.ea=d||"";this.S=this.A=this.G=null}function W(a){return a?a.__styleInfo:null}function zh(a,b){return a.__styleInfo=b}yh.prototype.Wb=function(){return this.B};yh.prototype._getStyleRules=yh.prototype.Wb;function Ah(a){var b=this.matches||this.matchesSelector||this.mozMatchesSelector||this.msMatchesSelector||this.oMatchesSelector||this.webkitMatchesSelector;return b&&b.call(this,a)}var Bh=/:host\s*>\s*/,Ch=navigator.userAgent.match("Trident");function Dh(){}function Eh(a){var b={},c=[],d=0;Ug(a,function(f){Fh(f);f.index=d++;f=f.l.cssText;for(var g;g=Og.exec(f);){var h=g[1];g[2]!==":"&&(b[h]=!0)}},function(f){c.push(f)});a.Zb=c;a=[];for(var e in b)a.push(e);return a}
function Fh(a){if(!a.l){var b={},c={};Gh(a,c)&&(b.F=c,a.rules=null);b.cssText=a.parsedCssText.replace(Rg,"").replace(Mg,"");a.l=b}}function Gh(a,b){var c=a.l;if(c){if(c.F)return Object.assign(b,c.F),!0}else{c=a.parsedCssText;for(var d;a=Mg.exec(c);){d=(a[2]||a[3]).trim();if(d!=="inherit"||d!=="unset")b[a[1].trim()]=d;d=!0}return d}}
function Hh(a,b,c){b&&(b=b.indexOf(";")>=0?Ih(a,b,c):bh(b,function(d,e,f,g){if(!e)return d+g;(e=Hh(a,c[e],c))&&e!=="initial"?e==="apply-shim-inherit"&&(e="inherit"):e=Hh(a,c[f]||f,c)||f;return d+(e||"")+g}));return b&&b.trim()||""}
function Ih(a,b,c){b=b.split(";");for(var d=0,e,f;d<b.length;d++)if(e=b[d]){Ng.lastIndex=0;if(f=Ng.exec(e))e=Hh(a,c[f[1]],c);else if(f=e.indexOf(":"),f!==-1){var g=e.substring(f);g=g.trim();g=Hh(a,g,c)||g;e=e.substring(0,f)+g}b[d]=e&&e.lastIndexOf(";")===e.length-1?e.slice(0,-1):e||""}return b.join(";")}
function Jh(a,b){var c={},d=[];Ug(a,function(e){e.l||Fh(e);var f=e.o||e.parsedSelector;b&&e.l.F&&f&&Ah.call(b,f)&&(Gh(e,c),e=e.index,f=parseInt(e/32,10),d[f]=(d[f]||0)|1<<e%32)},null,!0);return{F:c,key:d}}
function Kh(a,b,c,d){b.l||Fh(b);if(b.l.F){var e=eh(a);a=e.is;e=e.ea;e=a?V.ya(a,e):"html";var f=b.parsedSelector;var g=!!f.match(Bh)||e==="html"&&f.indexOf("html")>-1;var h=f.indexOf(":host")===0&&!g;c==="shady"&&(g=f===e+" > *."+e||f.indexOf("html")!==-1,h=!g&&f.indexOf(e)===0);if(g||h)c=e,h&&(b.o||(b.o=V.kb(b,V.Fa,V.Ra(a),e)),c=b.o||e),g&&e==="html"&&(c=b.o||b.gd),d({Ma:c,Lc:h,fd:g})}}
function Lh(a,b,c){var d={},e={};Ug(b,function(f){Kh(a,f,c,function(g){Ah.call(a._element||a,g.Ma)&&(g.Lc?Gh(f,d):Gh(f,e))})},null,!0);return{Tc:e,Kc:d}}
function Mh(a,b,c,d){var e=eh(b),f=V.ya(e.is,e.ea),g=new RegExp("(?:^|[^.#[:])"+(b.extends?"\\"+f.slice(0,-1)+"\\]":f)+"($|[.:[\\s>+~])"),h=W(b);e=h.B;h=h.cssBuild;var k=a.Mb(b,e,d);return nh(b,e,function(l){var p="";l.l||Fh(l);l.l.cssText&&(p=Ih(a,l.l.cssText,c));l.cssText=p;if(!T&&!Wg(l)&&l.cssText){var v=p=l.cssText;l.pb==null&&(l.pb=Pg.test(p));if(l.pb)if(l.sa==null){l.sa=[];for(var B in k)v=k[B],v=v(p),p!==v&&(p=v,l.sa.push(B))}else{for(B=0;B<l.sa.length;++B)v=k[l.sa[B]],p=v(p);v=p}l.cssText=
v;a.sc(l,g,f,d)}},h)}Dh.prototype.Mb=function(a,b,c){a=b.Zb;b={};if(!T&&a)for(var d=0,e=a[d];d<a.length;e=a[++d])this.rc(e,c),b[e.keyframesName]=this.ac(e);return b};Dh.prototype.ac=function(a){return function(b){return b.replace(a.Nc,a.yb)}};Dh.prototype.rc=function(a,b){a.Nc=new RegExp("\\b"+a.keyframesName+"(?!\\B|-)","g");a.yb=a.keyframesName+"-"+b;a.o=a.o||a.selector;a.selector=a.o.replace(a.keyframesName,a.yb)};
Dh.prototype.sc=function(a,b,c,d){a.o=a.o||a.selector;d="."+d;for(var e=fh(a.o),f=0,g=e.length,h=void 0;f<g&&(h=e[f]);f++)e[f]=h.match(b)?h.replace(c,d):d+" "+h;a.selector=e.join(",")};function Nh(a,b){var c=Oh,d=Vg(a);a.textContent=Tg(d,function(e){var f=e.cssText=e.parsedCssText;e.l&&e.l.cssText&&(f=f.replace(Ag,"").replace(Bg,""),e.cssText=Ih(c,f,b))})}ca.Object.defineProperties(Dh.prototype,{Ab:{configurable:!0,enumerable:!0,get:function(){return"x-scope"}}});var Oh=new Dh;var Ph={},Qh=window.customElements;if(Qh&&!T&&!Lg){var Rh=Qh.define;Qh.define=function(a,b,c){Ph[a]||(Ph[a]=$g(a));Rh.call(Qh,a,b,c)}};function Sh(){this.cache={};this.Zc=100}Sh.prototype.Ac=function(a,b,c){for(var d=0;d<c.length;d++){var e=c[d];if(a.F[e]!==b[e])return!1}return!0};Sh.prototype.store=function(a,b,c,d){var e=this.cache[a]||[];e.push({F:b,styleElement:c,A:d});e.length>this.Zc&&e.shift();this.cache[a]=e};Sh.prototype.fetch=function(a,b,c){if(a=this.cache[a])for(var d=a.length-1;d>=0;d--){var e=a[d];if(this.Ac(e,b,c))return e}};function Th(){}var Uh=new RegExp(V.fa+"\\s*([^\\s]*)");function Vh(a){return(a=(a.classList&&a.classList.value?a.classList.value:a.getAttribute("class")||"").match(Uh))?a[1]:""}function Wh(a){var b=dh(a).getRootNode();return b===a||b===a.ownerDocument?"":(a=b.host)?eh(a).is:""}
function Xh(a){for(var b=0;b<a.length;b++){var c=a[b];if(c.target!==document.documentElement&&c.target!==document.head)for(var d=0;d<c.addedNodes.length;d++){var e=c.addedNodes[d];if(e.nodeType===Node.ELEMENT_NODE){var f=e.getRootNode(),g=Vh(e);if(g&&f===e.ownerDocument&&(e.localName!=="style"&&e.localName!=="template"||gh(e)===""))mh(e,g);else if(f instanceof ShadowRoot)for(f=Wh(e),f!==g&&lh(e,g,f),e=window.ShadyDOM.nativeMethods.querySelectorAll.call(e,":not(."+V.fa+")"),g=0;g<e.length;g++){f=e[g];
var h=Wh(f);h&&V.element(f,h)}}}}}
if(!(T||window.ShadyDOM&&window.ShadyDOM.handlesDynamicScoping)){var Yh=new MutationObserver(Xh),Zh=function(a){Yh.observe(a,{childList:!0,subtree:!0})};if(window.customElements&&!window.customElements.polyfillWrapFlushCallback)Zh(document);else{var $h=function(){Zh(document.body)};window.HTMLImports?window.HTMLImports.whenReady($h):requestAnimationFrame(function(){if(document.readyState==="loading"){var a=function(){$h();document.removeEventListener("readystatechange",a)};document.addEventListener("readystatechange",
a)}else $h()})}Th=function(){Xh(Yh.takeRecords())}};var ai={};var bi=Promise.resolve();function ci(a){if(a=ai[a])a._applyShimCurrentVersion=a._applyShimCurrentVersion||0,a._applyShimValidatingVersion=a._applyShimValidatingVersion||0,a._applyShimNextVersion=(a._applyShimNextVersion||0)+1}function di(a){return a._applyShimCurrentVersion===a._applyShimNextVersion}function ei(a){a._applyShimValidatingVersion=a._applyShimNextVersion;a._validating||(a._validating=!0,bi.then(function(){a._applyShimCurrentVersion=a._applyShimNextVersion;a._validating=!1}))};var fi={},gi=new Sh;function X(){this.hb={};this.M=document.documentElement;var a=new sg;a.rules=[];this.C=zh(this.M,new yh(a));this.Aa=!1;this.h=this.m=null}n=X.prototype;n.flush=function(){Th()};n.Sb=function(a){var b=this.hb[a]=(this.hb[a]||0)+1;return a+"-"+b};n.Hc=function(a){return Vg(a)};n.Yc=function(a){return Tg(a)};
n.Rb=function(a){var b=[];a=a.content.querySelectorAll("style");for(var c=0;c<a.length;c++){var d=a[c];if(d.hasAttribute("shady-unscoped")){if(!T){var e=d.textContent;if(!Sg.has(e)){Sg.add(e);var f=document.createElement("style");f.setAttribute("shady-unscoped","");f.textContent=e;document.head.appendChild(f)}d.parentNode.removeChild(d)}}else b.push(d.textContent),d.parentNode.removeChild(d)}return b.join("").trim()};
n.prepareTemplate=function(a,b,c){this.prepareTemplateDom(a,b);this.prepareTemplateStyles(a,b,c)};
n.prepareTemplateStyles=function(a,b,c){if(!a._prepared&&!Lg){T||Ph[b]||(Ph[b]=$g(b));a._prepared=!0;a.name=b;a.extends=c;ai[b]=a;var d=gh(a),e=hh(d);c={is:b,extends:c};var f=this.Rb(a)+(fi[b]||"");this.W();if(!e){var g;if(g=!d)g=Ng.test(f)||Mg.test(f),Ng.lastIndex=0,Mg.lastIndex=0;var h=tg(f);g&&U&&this.m&&this.m.transformRules(h,b);a._styleAst=h}g=[];U||(g=Eh(a._styleAst));if(!g.length||U)b=this.Tb(c,a._styleAst,T?a.content:null,Ph[b]||null,d,e?f:""),a._style=b;a.fc=g}};
n.prepareAdoptedCssText=function(a,b){fi[b]=a.join(" ")};n.prepareTemplateDom=function(a,b){if(!Lg){var c=gh(a);T||c==="shady"||a._domPrepared||(a._domPrepared=!0,kh(a.content,b))}};n.Tb=function(a,b,c,d,e,f){f=nh(a,b,null,e,f);return f.length?Xg(f,a.is,c,d):null};n.eb=function(a){var b=eh(a),c=b.is;b=b.ea;var d=Ph[c]||null,e=ai[c];if(e){c=e._styleAst;var f=e.fc;e=gh(e);b=new yh(c,d,f,b,e);zh(a,b);return b}};
n.Nb=function(){return!this.m&&window.ShadyCSS&&window.ShadyCSS.ApplyShim?(this.m=window.ShadyCSS.ApplyShim,this.m.invalidCallback=ci,!0):!1};n.Ob=function(){var a=this;!this.h&&window.ShadyCSS&&window.ShadyCSS.CustomStyleInterface&&(this.h=window.ShadyCSS.CustomStyleInterface,this.h.transformCallback=function(b){a.xb(b)},this.h.validateCallback=function(){requestAnimationFrame(function(){(a.h.enqueued||a.Aa)&&a.flushCustomStyles()})})};n.W=function(){var a=this.Nb();this.Ob();return a};
n.flushCustomStyles=function(){if(!Lg){var a=this.W();if(this.h){var b=this.h.processStyles();!a&&!this.h.enqueued||hh(this.C.cssBuild)||(U?this.C.cssBuild||this.qc(b):(this.lc(b),this.Ga(this.M,this.C),this.Fb(b),this.Aa&&this.styleDocument()),this.h.enqueued=!1)}}};
n.lc=function(a){var b=this;a=a.map(function(c){return b.h.getStyleForCustomStyle(c)}).filter(function(c){return!!c});a.sort(function(c,d){c=d.compareDocumentPosition(c);return c&Node.DOCUMENT_POSITION_FOLLOWING?1:c&Node.DOCUMENT_POSITION_PRECEDING?-1:0});this.C.B.rules=a.map(function(c){return Vg(c)})};
n.styleElement=function(a,b){if(Lg){if(b){W(a)||zh(a,new yh(null));var c=W(a);this.bb(c,b);hi(this,a,c)}}else if(c=W(a)||this.eb(a))this.Ba(a)||(this.Aa=!0),b&&this.bb(c,b),U?hi(this,a,c):(this.flush(),this.Ga(a,c),c.Ka&&c.Ka.length&&this.Gb(a,c))};n.bb=function(a,b){a.U=a.U||{};Object.assign(a.U,b)};
function hi(a,b,c){var d=eh(b).is;if(c.U){var e=c.U,f;for(f in e)f===null?b.style.removeProperty(f):b.style.setProperty(f,e[f])}if(((e=ai[d])||a.Ba(b))&&(!e||gh(e)==="")&&e&&e._style&&!di(e)){if(di(e)||e._applyShimValidatingVersion!==e._applyShimNextVersion)a.W(),a.m&&a.m.transformRules(e._styleAst,d),e._style.textContent=nh(b,c.B),ei(e);T&&(a=b.shadowRoot)&&(a=a.querySelector("style"))&&(a.textContent=nh(b,c.B));c.B=e._styleAst}}
n.Ea=function(a){return(a=dh(a).getRootNode().host)?W(a)||this.eb(a)?a:this.Ea(a):this.M};n.Ba=function(a){return a===this.M};
n.Gb=function(a,b){var c=eh(a).is,d=gi.fetch(c,b.G,b.Ka),e=d?d.styleElement:null,f=b.A;b.A=d&&d.A||this.Sb(c);var g=b.G;var h=b.A;var k=Oh;g=e?e.textContent||"":Mh(k,a,g,h);k=W(a);var l=k.S;l&&!T&&l!==e&&(l._useCount--,l._useCount<=0&&l.parentNode&&l.parentNode.removeChild(l));T?k.S?(k.S.textContent=g,e=k.S):g&&(e=Xg(g,h,a.shadowRoot,k.placeholder)):e?e.parentNode||(Ch&&g.indexOf("@media")>-1&&(e.textContent=g),Yg(e,null,k.placeholder)):g&&(e=Xg(g,h,null,k.placeholder));e&&(e._useCount=e._useCount||
0,k.S!=e&&e._useCount++,k.S=e);h=e;T||(e=b.A,k=g=a.getAttribute("class")||"",f&&(k=g.replace(new RegExp("\\s*x-scope\\s*"+f+"\\s*","g")," ")),k+=(k?" ":"")+"x-scope "+e,g!==k&&ch(a,k));d||gi.store(c,b.G,h,b.A);return h};
n.Ga=function(a,b){var c=this.Ea(a),d=W(c),e=d.G;c===this.M||e||(this.Ga(c,d),e=d.G);c=Object.create(e||null);e=Lh(a,b.B,b.cssBuild);a=Jh(d.B,a).F;Object.assign(c,e.Kc,a,e.Tc);this.dc(c,b.U);a=Oh;d=Object.getOwnPropertyNames(c);e=0;for(var f;e<d.length;e++)f=d[e],c[f]=Hh(a,c[f],c);b.G=c};n.dc=function(a,b){for(var c in b){var d=b[c];if(d||d===0)a[c]=d}};n.styleDocument=function(a){this.styleSubtree(this.M,a)};
n.styleSubtree=function(a,b){var c=dh(a),d=c.shadowRoot,e=this.Ba(a);(d||e)&&this.styleElement(a,b);if(a=e?c:d)for(a=Array.from(a.querySelectorAll("*")).filter(function(f){return dh(f).shadowRoot}),b=0;b<a.length;b++)this.styleSubtree(a[b])};n.qc=function(a){for(var b=0;b<a.length;b++){var c=this.h.getStyleForCustomStyle(a[b]);c&&this.oc(c)}};n.Fb=function(a){for(var b=0;b<a.length;b++){var c=this.h.getStyleForCustomStyle(a[b]);c&&Nh(c,this.C.G)}};
n.xb=function(a){var b=this,c=gh(a);c!==this.C.cssBuild&&(this.C.cssBuild=c);if(!hh(c)){var d=Vg(a);Ug(d,function(e){if(T)xh(e);else{var f=V;e.selector=e.parsedSelector;xh(e);f.jb(e,f.vc)}U&&c===""&&(b.W(),b.m&&b.m.transformRule(e))});U?a.textContent=Tg(d):this.C.B.rules.push(d)}};n.oc=function(a){if(U&&this.m){var b=Vg(a);this.W();this.m.transformRules(b);a.textContent=Tg(b)}};
n.getComputedStyleValue=function(a,b){var c;U||(c=(W(a)||W(this.Ea(a))).G[b]);return(c=c||window.getComputedStyle(a).getPropertyValue(b))?c.trim():""};n.Xc=function(a,b){var c=dh(a).getRootNode();b=b?(typeof b==="string"?b:String(b)).split(/\s/):[];c=c.host&&c.host.localName;if(!c){var d=a.getAttribute("class");if(d){d=d.split(/\s/);for(var e=0;e<d.length;e++)if(d[e]===V.fa){c=d[e+1];break}}}c&&b.push(V.fa,c);U||(c=W(a))&&c.A&&b.push(Oh.Ab,c.A);ch(a,b.join(" "))};n.tc=function(a){return W(a)};
n.Wc=function(a,b){V.element(a,b)};n.ad=function(a,b){V.element(a,b,!0)};n.Vc=function(a){return Wh(a)};n.Ec=function(a){return Vh(a)};X.prototype.flush=X.prototype.flush;X.prototype.prepareTemplate=X.prototype.prepareTemplate;X.prototype.styleElement=X.prototype.styleElement;X.prototype.styleDocument=X.prototype.styleDocument;X.prototype.styleSubtree=X.prototype.styleSubtree;X.prototype.getComputedStyleValue=X.prototype.getComputedStyleValue;X.prototype.setElementClass=X.prototype.Xc;
X.prototype._styleInfoForNode=X.prototype.tc;X.prototype.transformCustomStyleForDocument=X.prototype.xb;X.prototype.getStyleAst=X.prototype.Hc;X.prototype.styleAstToString=X.prototype.Yc;X.prototype.flushCustomStyles=X.prototype.flushCustomStyles;X.prototype.scopeNode=X.prototype.Wc;X.prototype.unscopeNode=X.prototype.ad;X.prototype.scopeForNode=X.prototype.Vc;X.prototype.currentScopeForNode=X.prototype.Ec;X.prototype.prepareAdoptedCssText=X.prototype.prepareAdoptedCssText;
Object.defineProperties(X.prototype,{nativeShadow:{get:function(){return T}},nativeCss:{get:function(){return U}}});var Y=new X,ii,ji;window.ShadyCSS&&(ii=window.ShadyCSS.ApplyShim,ji=window.ShadyCSS.CustomStyleInterface);
window.ShadyCSS={ScopingShim:Y,prepareTemplate:function(a,b,c){Y.flushCustomStyles();Y.prepareTemplate(a,b,c)},prepareTemplateDom:function(a,b){Y.prepareTemplateDom(a,b)},prepareTemplateStyles:function(a,b,c){Y.flushCustomStyles();Y.prepareTemplateStyles(a,b,c)},styleSubtree:function(a,b){Y.flushCustomStyles();Y.styleSubtree(a,b)},styleElement:function(a){Y.flushCustomStyles();Y.styleElement(a)},styleDocument:function(a){Y.flushCustomStyles();Y.styleDocument(a)},flushCustomStyles:function(){Y.flushCustomStyles()},
getComputedStyleValue:function(a,b){return Y.getComputedStyleValue(a,b)},nativeCss:U,nativeShadow:T,cssBuild:Gg,disableRuntime:Lg};ii&&(window.ShadyCSS.ApplyShim=ii);ji&&(window.ShadyCSS.CustomStyleInterface=ji);/*

Copyright (c) 2019 The Polymer Project Authors. All rights reserved.
This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
Code distributed by Google as part of the polymer project is also
subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
*/
var ki=window.customElements,li=window.HTMLImports,mi=!1,ni=null;ki.polyfillWrapFlushCallback&&ki.polyfillWrapFlushCallback(function(a){ni=a;mi&&a()});function oi(){li.whenReady(function(){ni&&ni();mi=!0;window.WebComponents.ready=!0;document.dispatchEvent(new CustomEvent("WebComponentsReady",{bubbles:!0}))})}document.readyState!=="complete"?(window.addEventListener("load",oi),window.addEventListener("DOMContentLoaded",function(){window.removeEventListener("load",oi);oi()})):oi();})();
//# sourceMappingURL=webcomponents-lite.js.map
